document.addEventListener('DOMContentLoaded', () => {
    // DOM elements
    const chatMessages = document.getElementById('chat-messages');
    const listeningIndicator = document.getElementById('listening-indicator');
    const statusText = document.getElementById('status-text');
    const audioPlayer = document.getElementById('audio-player');
    const audioPermissionOverlay = document.getElementById('audio-permission-overlay');
    const enableAudioBtn = document.getElementById('enable-audio-btn');

    // Always show the animation
    listeningIndicator.classList.add('active');

    // Audio context and permission variables
    let audioContextInitialized = false;
    let userInteractionOccurred = false;

    // Handle the enable audio button click - Zoom style
    enableAudioBtn.addEventListener('click', () => {
        // Hide the button to prevent multiple clicks
        enableAudioBtn.disabled = true;
        enableAudioBtn.textContent = 'Requesting permissions...';

        // Mark that user interaction has occurred
        userInteractionOccurred = true;
        updateDebugStatus('User clicked Join with Audio button');

        // Request microphone permission first
        navigator.mediaDevices.getUserMedia({ audio: true })
            .then(stream => {
                updateDebugStatus('Microphone permission granted');

                // Stop the tracks immediately - we just needed the permission
                stream.getTracks().forEach(track => track.stop());

                // Now initialize audio context for playback
                initializeAudioContext();

                // Hide the permission overlay with a slight delay to match Zoom's behavior
                setTimeout(() => {
                    audioPermissionOverlay.style.display = 'none';
                    startListening();
                    updateDebugStatus('Audio enabled, starting speech recognition');
                }, 500);
            })
            .catch(err => {
                console.error('Microphone permission denied:', err);
                updateDebugStatus('Microphone permission denied: ' + err.message);

                // Update the button to show the error
                enableAudioBtn.textContent = 'Microphone access denied';
                setTimeout(() => {
                    enableAudioBtn.disabled = false;
                    enableAudioBtn.textContent = 'Try Again';
                }, 2000);
            });
    });

    // Socket.io connection
    const socket = io();

    // Function to initialize audio context - Zoom style
    function initializeAudioContext() {
        try {
            // Create and resume AudioContext to enable audio playback
            const AudioContext = window.AudioContext || window.webkitAudioContext;
            const audioContext = new AudioContext();

            if (audioContext.state === 'suspended') {
                audioContext.resume();
            }

            console.log('AudioContext initialized:', audioContext.state);
            updateDebugStatus('Audio system initialized: ' + audioContext.state);

            // Create a silent sound to unlock audio on iOS
            const buffer = audioContext.createBuffer(1, 1, 22050);
            const source = audioContext.createBufferSource();
            source.buffer = buffer;
            source.connect(audioContext.destination);
            source.start(0);

            console.log('Audio unlocked for playback');

            // Play a very short, silent test sound to verify audio works
            // This is similar to how Zoom tests audio without making an audible sound
            audioPlayer.src = 'data:audio/wav;base64,UklGRigAAABXQVZFZm10IBIAAAABAAEARKwAAIhYAQACABAAAABkYXRhAgAAAAEA';
            audioPlayer.volume = 0.01; // Nearly silent

            const playPromise = audioPlayer.play();

            if (playPromise !== undefined) {
                playPromise
                    .then(() => {
                        console.log('Audio system check successful');
                        audioContextInitialized = true;
                        updateDebugStatus('Audio system check successful');
                    })
                    .catch(e => {
                        console.warn('Audio system check failed:', e);
                        updateDebugStatus('Audio system check failed - additional permissions may be needed');

                        // Even if the test fails, we'll still try to proceed
                        // This matches Zoom's behavior of continuing even if audio test fails
                        audioContextInitialized = true;
                    });
            }
        } catch (e) {
            console.warn('Could not initialize audio system:', e);
            updateDebugStatus('Audio system initialization failed: ' + e.message);
        }
    }

    // Variables for recording
    let mediaRecorder;
    let audioChunks = [];
    let isListening = false;
    let isSpeaking = false;
    let silenceTimeout;
    let silenceStart = null;
    let audioContext;
    let analyser;
    let silenceThreshold = 0.01; // Adjust based on testing
    let silenceTime = 1500; // 1.5 seconds of silence to trigger end

    // Speech recognition
    let recognition;
    if ('webkitSpeechRecognition' in window) {
        recognition = new webkitSpeechRecognition();
        recognition.continuous = true;
        recognition.interimResults = true;

        recognition.onstart = () => {
            console.log('Speech recognition started');
            // Always keep animation active
            listeningIndicator.classList.add('active');
        };

        recognition.onresult = (event) => {
            let finalTranscript = '';
            let interimTranscript = '';

            for (let i = event.resultIndex; i < event.results.length; i++) {
                const transcript = event.results[i][0].transcript;
                if (event.results[i].isFinal) {
                    finalTranscript += transcript;
                } else {
                    interimTranscript += transcript;
                }
            }

            if (finalTranscript) {
                console.log('Final transcript:', finalTranscript);
                sendVoiceMessage(finalTranscript);
            } else if (interimTranscript) {
                console.log('Interim transcript:', interimTranscript);
            }
        };

        recognition.onerror = (event) => {
            console.error('Speech recognition error:', event.error);
            restartSpeechRecognition();
        };

        recognition.onend = () => {
            console.log('Speech recognition ended');
            restartSpeechRecognition();
        };
    } else {
        console.error('Speech recognition not supported');
        statusText.textContent = 'Speech recognition not supported in this browser';
    }

    // No welcome message in the minimalist UI

    // Check if we can start listening or need user interaction first
    if (document.visibilityState === 'visible') {
        // Try to initialize audio - this will show the permission overlay if needed
        setTimeout(() => {
            // Don't start listening immediately - wait for user interaction
            // startListening() will be called after user clicks the enable button
            updateDebugStatus('Ready - waiting for user interaction');
        }, 500);
    } else {
        // Wait for the page to become visible
        document.addEventListener('visibilitychange', () => {
            if (document.visibilityState === 'visible' && !userInteractionOccurred) {
                updateDebugStatus('Page now visible - waiting for user interaction');
            }
        });
    }

    // Socket events
    socket.on('connect', () => {
        console.log('Connected to server');
        updateDebugStatus('Connected to server');
    });

    socket.on('disconnect', () => {
        console.log('Disconnected from server');
        updateDebugStatus('Disconnected from server');
    });

    socket.on('audio_status', (data) => {
        console.log('Audio status:', data);
        updateDebugStatus('Audio status: ' + JSON.stringify(data));
    });

    socket.on('error', (data) => {
        console.error('Server error:', data.message);
        addMessage(`Error: ${data.message}`, 'ai');
        updateDebugStatus('Server error: ' + data.message);
    });

    // Debug panel functionality
    const debugPanel = document.getElementById('debug-panel');
    const testMessageBtn = document.getElementById('test-message-btn');
    const toggleRecognitionBtn = document.getElementById('toggle-recognition-btn');
    const testAudioBtn = document.getElementById('test-audio-btn');
    const debugStatus = document.getElementById('debug-status');

    // Double-click anywhere to show debug panel
    document.addEventListener('dblclick', () => {
        debugPanel.classList.toggle('visible');
        updateDebugStatus('Debug panel toggled');
    });

    // Test message button
    testMessageBtn.addEventListener('click', () => {
        updateDebugStatus('Sending test message...');
        sendVoiceMessage('Tell me about the latest Claude AI model');
    });

    // Toggle recognition button
    toggleRecognitionBtn.addEventListener('click', () => {
        updateDebugStatus('Restarting speech recognition...');
        stopListening();
        setTimeout(() => {
            startListening();
        }, 500);
    });

    // Test audio button
    testAudioBtn.addEventListener('click', () => {
        updateDebugStatus('Testing audio playback...');

        // Set userInteractionOccurred to true since user clicked a button
        userInteractionOccurred = true;

        // Create a test audio file on the server using real TTS
        updateDebugStatus('Creating test audio file on server...');
        fetch('/api/create-test-audio', {
            method: 'POST'
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                updateDebugStatus('Test audio file created: ' + data.path);

                // Try to play the file
                const audioUrl = data.url.startsWith('/')
                    ? window.location.origin + data.url
                    : data.url;

                updateDebugStatus('Trying to play server audio file: ' + audioUrl);

                // Create a new audio element for this test
                const serverAudio = new Audio(audioUrl);
                serverAudio.volume = 1.0;

                serverAudio.onended = () => {
                    updateDebugStatus('Server audio playback completed');
                };

                serverAudio.onerror = (error) => {
                    updateDebugStatus('Error playing server audio: ' + (serverAudio.error ? serverAudio.error.code : 'unknown'));
                };

                serverAudio.play()
                    .then(() => updateDebugStatus('Server audio playback started'))
                    .catch(e => updateDebugStatus('Failed to play server audio: ' + e));
            } else {
                updateDebugStatus('Error creating test audio: ' + data.error);
            }
        })
        .catch(error => {
            updateDebugStatus('Error: ' + error);
        });
    });

    // Check audio directory button
    const checkAudioDirBtn = document.getElementById('check-audio-dir-btn');
    checkAudioDirBtn.addEventListener('click', () => {
        updateDebugStatus('Checking audio directory...');

        fetch('/api/check-audio-dir')
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    const fileList = data.files.map(file =>
                        `${file.name} (${file.size} bytes, exists: ${file.exists})`
                    ).join('\n');

                    updateDebugStatus(`Audio directory: ${data.directory}\n` +
                        `Directory exists: ${data.exists}\n` +
                        `Is directory: ${data.is_dir}\n` +
                        `File count: ${data.file_count}\n\n` +
                        `Files:\n${fileList}`);

                    // Try to play the first audio file if any exist
                    if (data.files.length > 0) {
                        const firstAudioFile = data.files[0];
                        const audioUrl = firstAudioFile.url.startsWith('/')
                            ? window.location.origin + firstAudioFile.url
                            : firstAudioFile.url;

                        updateDebugStatus(`Trying to play first audio file: ${audioUrl}`);

                        // Create a new audio element for this test
                        const directoryAudio = new Audio(audioUrl);
                        directoryAudio.volume = 1.0;

                        directoryAudio.onended = () => {
                            updateDebugStatus('Directory audio playback completed');
                        };

                        directoryAudio.onerror = (error) => {
                            updateDebugStatus('Error playing directory audio: ' +
                                (directoryAudio.error ? directoryAudio.error.code : 'unknown'));
                        };

                        directoryAudio.play()
                            .then(() => updateDebugStatus('Directory audio playback started'))
                            .catch(e => updateDebugStatus('Failed to play directory audio: ' + e));
                    }
                } else {
                    updateDebugStatus('Error checking audio directory: ' + data.error);
                }
            })
            .catch(error => {
                updateDebugStatus('Error: ' + error);
            });
    });

    function updateDebugStatus(message) {
        if (debugStatus) {
            const timestamp = new Date().toLocaleTimeString();
            debugStatus.textContent = `[${timestamp}] ${message}`;
        }
    }

    // Functions
    function startListening() {
        if (isListening) return;

        // If user hasn't interacted yet, show the permission overlay (Zoom style)
        if (!userInteractionOccurred) {
            console.log('User interaction required for speech recognition');
            updateDebugStatus('Waiting for user to join with audio');
            audioPermissionOverlay.style.display = 'flex';
            return;
        }

        if (recognition) {
            try {
                // Add a slight delay before starting recognition
                // This matches Zoom's behavior of a slight pause after permissions
                updateDebugStatus('Initializing speech recognition...');

                setTimeout(() => {
                    recognition.start();
                    isListening = true;
                    console.log('Started listening');
                    updateDebugStatus('Voice chat active');

                    // Show a brief status message that fades out
                    const statusElement = document.createElement('div');
                    statusElement.className = 'status-popup';
                    statusElement.textContent = 'Voice chat active';
                    document.body.appendChild(statusElement);

                    // Fade out and remove after 3 seconds
                    setTimeout(() => {
                        statusElement.classList.add('fade-out');
                        setTimeout(() => statusElement.remove(), 1000);
                    }, 2000);
                }, 500);
            } catch (error) {
                console.error('Error starting speech recognition:', error);
                updateDebugStatus('Error starting speech recognition: ' + error.message);
                setTimeout(restartSpeechRecognition, 1000);
            }
        }
    }

    function stopListening() {
        if (!isListening) return;

        if (recognition) {
            try {
                recognition.stop();
                isListening = false;
                // Keep the animation active even when not listening
                // listeningIndicator.classList.remove('active');
                console.log('Stopped listening');
            } catch (error) {
                console.error('Error stopping speech recognition:', error);
            }
        }
    }

    function restartSpeechRecognition() {
        // Don't restart if we're speaking
        if (isSpeaking) {
            console.log('Not restarting speech recognition while speaking');
            return;
        }

        // Wait a moment before restarting
        setTimeout(() => {
            if (!isListening && !isSpeaking) {
                startListening();
            }
        }, 300);
    }

    function sendVoiceMessage(message) {
        if (!message) return;

        console.log('Sending voice message to server:', message);

        // Temporarily stop listening while processing
        stopListening();

        // Add user message to chat
        addMessage(message, 'user');

        // Send message to server
        fetch('/api/chat', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({
                message: message,
                username: 'User'
            })
        })
        .then(response => response.json())
        .then(data => {
            if (data.error) {
                addMessage(`Error: ${data.error}`, 'ai');
            } else {
                // Add AI response to chat
                addMessage(data.response, 'ai');

                // Play audio if available
                if (data.audio_url) {
                    isSpeaking = true;
                    // Keep animation active even when AI is speaking
                    listeningIndicator.classList.add('active');

                    // Log the audio URL for debugging
                    console.log('Audio URL to play:', data.audio_url);
                    updateDebugStatus('Attempting to play audio from: ' + data.audio_url);

                    // Make sure the audio URL is absolute
                    const audioUrl = data.audio_url.startsWith('/')
                        ? window.location.origin + data.audio_url
                        : data.audio_url;

                    console.log('Full audio URL:', audioUrl);
                    updateDebugStatus('Full audio URL: ' + audioUrl);

                    playAudio(audioUrl).then(() => {
                        isSpeaking = false;
                        // Restart listening after audio finishes
                        restartSpeechRecognition();
                    });
                } else {
                    // If no audio, restart listening immediately
                    console.log('No audio URL provided');
                    updateDebugStatus('No audio URL provided');
                    restartSpeechRecognition();
                }
            }
        })
        .catch(error => {
            console.error('Error:', error);
            addMessage('Sorry, there was an error processing your request.', 'ai');
            restartSpeechRecognition();
        });
    }

    function addMessage(text, sender) {
        console.log(`Message from ${sender}: ${text.substring(0, 100)}${text.length > 100 ? '...' : ''}`);
        updateDebugStatus(`New ${sender} message: ${text.substring(0, 30)}...`);

        const messageElement = document.createElement('div');
        messageElement.classList.add('message', `${sender}-message`);

        const messageText = document.createElement('div');
        messageText.classList.add('message-text');
        messageText.textContent = text;

        const messageTime = document.createElement('div');
        messageTime.classList.add('message-time');
        messageTime.textContent = new Date().toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' });

        messageElement.appendChild(messageText);
        messageElement.appendChild(messageTime);

        chatMessages.appendChild(messageElement);

        // Scroll to bottom
        chatMessages.scrollTop = chatMessages.scrollHeight;
}

function playAudio(url) {
    return new Promise((resolve) => {
        console.log('Playing audio from URL:', url);
        updateDebugStatus('Attempting to play audio...');

        // If user hasn't interacted yet, show the permission overlay (Zoom style)
        if (!userInteractionOccurred) {
            console.log('User interaction required for audio playback');
            updateDebugStatus('User interaction required for audio');
            audioPermissionOverlay.style.display = 'flex';

            // Store the URL we were trying to play
            const pendingAudioUrl = url;

            // When the user grants permission, we'll automatically play the pending audio
            const originalOnClick = enableAudioBtn.onclick;
            enableAudioBtn.onclick = function(e) {
                // Call the original handler first
                if (originalOnClick) {
                    // Use call to preserve this context
                    originalOnClick.call(this, e);
                }

                // After permissions are granted and overlay is hidden, play the audio
                const checkPermissionInterval = setInterval(() => {
                    if (userInteractionOccurred && audioPermissionOverlay.style.display === 'none') {
                        clearInterval(checkPermissionInterval);
                        setTimeout(() => {
                            playAudio(pendingAudioUrl).then(resolve);
                        }, 1000); // Slight delay to ensure permissions are fully processed
                    }
                }, 500);

                // Safety timeout to prevent infinite checking
                setTimeout(() => clearInterval(checkPermissionInterval), 10000);
            };
            return;
        }

        // Create a new audio element for this playback to avoid issues with reusing the same element
        const tempAudio = new Audio();

        // Set the source
        tempAudio.src = url;
        audioPlayer.src = url; // Also set the main player for backup

        // Set volume to maximum to ensure it's audible
        tempAudio.volume = 1.0;
        audioPlayer.volume = 1.0;

        // Set up event handlers
        tempAudio.onended = () => {
            console.log('Audio playback ended');
            updateDebugStatus('Audio playback completed');
            resolve();
        };

        // Handle errors
        tempAudio.onerror = (error) => {
            console.error('Error playing audio with temp element:', error);
            console.error('Audio error code:', tempAudio.error ? tempAudio.error.code : 'unknown');
            updateDebugStatus('Temp audio error: ' + (tempAudio.error ? tempAudio.error.code : 'unknown'));

            // Try with the main audio player as fallback
            console.log('Trying with main audio player as fallback');
            updateDebugStatus('Trying with main audio player as fallback');

            audioPlayer.onended = () => {
                console.log('Main audio player: playback ended');
                updateDebugStatus('Main audio player: playback completed');
                resolve();
            };

            audioPlayer.onerror = (mainError) => {
                console.error('Error playing audio with main player:', mainError);
                updateDebugStatus('Both audio players failed - resolving anyway');
                resolve(); // Resolve anyway to continue the flow
            };

            audioPlayer.play().catch(e => {
                console.error('Main audio player also failed:', e);
                resolve(); // Resolve anyway to continue the flow
            });
        };

        // Log when audio is loaded
        tempAudio.onloadeddata = () => {
            console.log('Audio data loaded successfully');
            updateDebugStatus('Audio data loaded, attempting playback');

            // Try to play when data is loaded
            tempAudio.play()
                .then(() => {
                    console.log('Audio started playing on data load');
                    updateDebugStatus('Audio playback started');
                })
                .catch(e => {
                    console.log('Auto-play attempt failed on data load:', e);
                    updateDebugStatus('Autoplay failed - trying main audio player');

                    // Try with the main audio player
                    audioPlayer.play()
                        .then(() => {
                            console.log('Main audio player started successfully');
                            updateDebugStatus('Main audio player started successfully');
                        })
                        .catch(mainError => {
                            console.error('Both audio players failed to autoplay:', mainError);
                            updateDebugStatus('Both players failed - showing permission overlay');

                            // Show permission overlay if playback fails
                            audioPermissionOverlay.style.display = 'flex';
                        });
                });
        };

        // Start playback immediately
        console.log('Attempting to play audio immediately...');
        tempAudio.play()
            .then(() => {
                console.log('Audio playback started successfully');
                updateDebugStatus('Audio playback started successfully');
            })
            .catch(error => {
                console.error('Error starting audio playback with temp player:', error);
                updateDebugStatus('Temp audio playback failed - trying main player');

                // Try with the main audio player
                audioPlayer.play()
                    .then(() => {
                        console.log('Main audio player started successfully');
                        updateDebugStatus('Main audio player started successfully');
                    })
                    .catch(mainError => {
                        console.error('Both audio players failed to start:', mainError);
                        updateDebugStatus('Both players failed - user interaction needed');

                        // Show permission overlay if playback fails
                        audioPermissionOverlay.style.display = 'flex';
                    });
            });
    });
}
});
