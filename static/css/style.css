/* Base styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    line-height: 1.6;
    color: #333;
    background-color: #1a1a1a; /* Dark background as requested */
    height: 100vh;
    display: flex;
    flex-direction: column;
}

.container {
    width: 100%;
    height: 100%;
    display: flex;
    justify-content: center;
    align-items: center;
}

.hidden {
    display: none !important;
}

.voice-animation-container {
    display: flex;
    justify-content: center;
    align-items: center;
    height: 100%;
}

/* Chat elements are positioned off-screen but still functional */
.chat-messages {
    position: fixed;
    top: -9999px;
    left: -9999px;
    width: 1px;
    height: 1px;
    overflow: hidden;
    opacity: 0.01; /* Not completely invisible for debugging */
}

.message {
    /* Still styled but off-screen */
    max-width: 80%;
    padding: 10px 15px;
    border-radius: 18px;
    position: relative;
    word-wrap: break-word;
    margin-bottom: 10px;
}

.user-message {
    align-self: flex-end;
    background-color: #4a6ee0;
    color: white;
}

.ai-message {
    align-self: flex-start;
    background-color: #e9e9eb;
    color: #333;
}

.message-time {
    font-size: 0.7rem;
    opacity: 0.7;
    margin-top: 5px;
    text-align: right;
}

/* Listening indicator styles */
.listening-indicator {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 15px;
    background-color: transparent;
    height: 80px;
}

.listening-animation {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 8px; /* Slightly wider gap */
}

.listening-animation span {
    display: inline-block;
    width: 8px; /* Slightly larger dots */
    height: 8px;
    background-color: #6466E9; /* Voice color as requested */
    border-radius: 50%;
    opacity: 0.3;
}

.listening-indicator.active .listening-animation span {
    animation: wave 1.5s infinite ease-in-out;
}

.listening-indicator.active .listening-animation span:nth-child(1) {
    animation-delay: 0s;
}

.listening-indicator.active .listening-animation span:nth-child(2) {
    animation-delay: 0.2s;
}

.listening-indicator.active .listening-animation span:nth-child(3) {
    animation-delay: 0.4s;
}

.listening-indicator.active .listening-animation span:nth-child(4) {
    animation-delay: 0.6s;
}

.listening-indicator.active .listening-animation span:nth-child(5) {
    animation-delay: 0.8s;
}

.status-text {
    display: none; /* Hide the status text */
}

/* Animations */
@keyframes pulse {
    0% {
        transform: scale(1);
    }
    50% {
        transform: scale(1.1);
    }
    100% {
        transform: scale(1);
    }
}

@keyframes wave {
    0% {
        transform: scaleY(1);
        opacity: 0.3;
    }
    50% {
        transform: scaleY(4);
        opacity: 1;
    }
    100% {
        transform: scaleY(1);
        opacity: 0.3;
    }
}

/* Audio player styles - hidden but functional */
.audio-container {
    position: absolute;
    width: 1px;
    height: 1px;
    overflow: hidden;
    opacity: 0;
}

audio {
    width: 1px;
    height: 1px;
}

#audio-player {
    width: 1px;
    height: 1px;
    opacity: 0;
}

/* Audio permission overlay - Zoom style */
.audio-permission-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.7);
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: 2000;
    backdrop-filter: blur(5px);
}

.permission-content.zoom-style {
    background-color: #2d2d2d;
    padding: 24px;
    border-radius: 8px;
    max-width: 420px;
    color: white;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.5);
}

.permission-header {
    display: flex;
    align-items: center;
    margin-bottom: 16px;
}

.permission-icon {
    margin-right: 12px;
    background-color: rgba(100, 102, 233, 0.1);
    width: 40px;
    height: 40px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
}

.permission-icon svg {
    width: 24px;
    height: 24px;
}

.permission-content h3 {
    margin: 0;
    font-size: 18px;
    font-weight: 500;
    color: white;
}

.permission-content p {
    margin-bottom: 16px;
    line-height: 1.5;
    font-size: 14px;
    color: #e0e0e0;
}

.permission-note {
    background-color: rgba(100, 102, 233, 0.1);
    border-left: 3px solid #6466E9;
    padding: 12px;
    font-size: 13px !important;
    margin-bottom: 20px !important;
}

.permission-buttons {
    display: flex;
    justify-content: flex-end;
}

#enable-audio-btn {
    background-color: #6466E9;
    color: white;
    border: none;
    padding: 10px 20px;
    border-radius: 4px;
    font-size: 14px;
    font-weight: 500;
    cursor: pointer;
    transition: background-color 0.2s;
}

#enable-audio-btn:hover {
    background-color: #5254c7;
}

/* Status popup - similar to Zoom's status messages */
.status-popup {
    position: fixed;
    top: 20px;
    left: 50%;
    transform: translateX(-50%);
    background-color: rgba(0, 0, 0, 0.7);
    color: white;
    padding: 10px 20px;
    border-radius: 4px;
    font-size: 14px;
    z-index: 1000;
    animation: fadeIn 0.3s ease-in-out;
}

.status-popup.fade-out {
    animation: fadeOut 1s ease-in-out forwards;
}

@keyframes fadeIn {
    from { opacity: 0; transform: translate(-50%, -10px); }
    to { opacity: 1; transform: translate(-50%, 0); }
}

@keyframes fadeOut {
    from { opacity: 1; transform: translate(-50%, 0); }
    to { opacity: 0; transform: translate(-50%, -10px); }
}

/* Debug panel styles */
.debug-panel {
    position: fixed;
    bottom: 10px;
    right: 10px;
    background-color: rgba(0, 0, 0, 0.7);
    padding: 10px;
    border-radius: 5px;
    display: none;
    flex-direction: column;
    gap: 10px;
    z-index: 1000;
}

.debug-panel.visible {
    display: flex;
}

.debug-panel button {
    padding: 8px 12px;
    background-color: #6466E9;
    color: white;
    border: none;
    border-radius: 4px;
    cursor: pointer;
}

.debug-panel button:hover {
    background-color: #5254c7;
}

#debug-status {
    color: white;
    font-size: 12px;
    max-width: 300px;
    word-wrap: break-word;
    margin-top: 10px;
}

/* Responsive styles */
@media (max-width: 768px) {
    .container {
        padding: 10px;
    }

    .message {
        max-width: 90%;
    }
}
