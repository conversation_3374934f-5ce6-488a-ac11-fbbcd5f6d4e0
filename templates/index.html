<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Sidenvoice - AI Voice Chat</title>
    <link rel="stylesheet" href="{{ url_for('static', filename='css/style.css') }}">
</head>
<body>
    <div class="container">
        <!-- Zoom-style permission dialog -->
        <div id="audio-permission-overlay" class="audio-permission-overlay">
            <div class="permission-content zoom-style">
                <div class="permission-header">
                    <div class="permission-icon">
                        <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" width="24" height="24"><path fill="#6466E9" d="M12 1a5 5 0 0 1 5 5v6a5 5 0 0 1-10 0V6a5 5 0 0 1 5-5zm0 2a3 3 0 0 0-3 3v6a3 3 0 0 0 6 0V6a3 3 0 0 0-3-3zm-1 18a1 1 0 0 1 1 1v1a1 1 0 0 1-2 0v-1a1 1 0 0 1 1-1zm-6-6a1 1 0 0 1 1 1v3a1 1 0 0 1-2 0v-3a1 1 0 0 1 1-1zm12 0a1 1 0 0 1 1 1v3a1 1 0 0 1-2 0v-3a1 1 0 0 1 1-1z"/></svg>
                    </div>
                    <h3>Microphone and Speaker Access</h3>
                </div>
                <p>This app needs access to your microphone and speakers to enable voice interaction.</p>
                <p class="permission-note">When your browser asks for permission, please click "Allow".</p>
                <div class="permission-buttons">
                    <button id="enable-audio-btn">Join with Audio</button>
                </div>
            </div>
        </div>

        <div class="voice-animation-container">
            <div id="listening-indicator" class="listening-indicator">
                <div class="listening-animation">
                    <span></span>
                    <span></span>
                    <span></span>
                    <span></span>
                    <span></span>
                </div>
                <div id="status-text" class="status-text">Listening...</div>
            </div>
        </div>

        <!-- Hidden elements that are still needed for functionality -->
        <div id="chat-messages" class="chat-messages"></div>
        <div class="audio-container">
            <audio id="audio-player" autoplay></audio>
        </div>

        <!-- Debug button (hidden by default, double-click anywhere to show) -->
        <div id="debug-panel" class="debug-panel">
            <button id="test-message-btn">Send Test Message</button>
            <button id="toggle-recognition-btn">Restart Recognition</button>
            <div id="debug-status">Debug status will appear here</div>
        </div>
    </div>

    <script src="https://cdnjs.cloudflare.com/ajax/libs/socket.io/4.0.1/socket.io.js"></script>
    <script src="{{ url_for('static', filename='js/app.js') }}"></script>
</body>
</html>
