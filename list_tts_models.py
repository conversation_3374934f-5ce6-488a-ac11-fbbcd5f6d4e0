from TTS.api import TTS

print('Testing TTS functionality:')

# Try to load a specific model
print("\nTrying to load a specific model:")
try:
    model_name = "tts_models/en/ljspeech/tacotron2-DDC"
    model = TTS(model_name=model_name)
    print(f"Successfully loaded model: {model_name}")

    # Try to generate speech
    print("\nTrying to generate speech:")
    output_path = "test_tts_output.wav"
    model.tts_to_file(text="Hello, this is a test of the custom TTS system.", file_path=output_path)
    print(f"Successfully generated speech to: {output_path}")

except Exception as e:
    print(f"Error: {e}")
