# Sidenvoice - Discord Voice Chat Bot

A Discord bot that can join voice channels and record audio from users for later transcription.

## Features

- Join and leave voice channels
- Record audio from voice channels
- Save recordings as WAV files
- Configurable recording duration

## Requirements

- Python 3.8 or higher
- Discord Bot Token
- FFmpeg installed on your system

## Installation

1. Clone this repository
2. Install dependencies:
```
pip install discord.py[voice] python-dotenv pynacl discord-ext-voice-recv
```
3. Create a `.env` file in the project root directory and add your Discord bot token:
```
DISCORD_TOKEN=your_discord_token_here
```
4. Ensure FFmpeg is installed on your system

## Usage

1. Run the bot:
```
python main.py
```

2. Use the following commands in your Discord server:
   - `!join` - <PERSON><PERSON> joins your current voice channel
   - `!leave` - <PERSON><PERSON> leaves the voice channel
   - `!record [seconds]` - Start recording audio for the specified number of seconds (default: 10)

## Permissions

The bot requires the following permissions:
- Read Messages/View Channels
- Send Messages
- Connect to Voice Channels
- Speak in Voice Channels

## Future Enhancements

- Add transcription of recorded audio
- Implement user-specific recording
- Add automatic detection of when users stop speaking