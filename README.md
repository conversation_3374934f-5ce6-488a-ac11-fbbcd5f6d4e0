# Sidenvoice - Web-based AI Voice Chat Application

A web application that allows users to interact with an AI assistant using voice input and output.

## Features

- Real-time speech recognition using Whisper
- AI responses using Google's Gemini AI
- Text-to-speech using local Coqui TTS (no API key required)
- Web-based interface with voice input/output

## Requirements

- Python 3.8 or higher
- Google Gemini API Key
- FFmpeg installed on your system
- TTS Python package (installed via requirements.txt)

## Installation

1. Clone this repository
2. Install dependencies:
```
pip install -r requirements.txt
```
3. Create a `.env` file in the project root directory (copy from `.env.example`):
```
GEMINI_API_KEY=your_gemini_api_key_here
```
4. Ensure FFmpeg is installed on your system

## Usage

1. Run the application:
```
python app.py
```
2. Open your browser and navigate to `http://localhost:5006`

## Features

- **Voice Input**: Click and hold the microphone button to speak to the AI
- **Voice Output**: Hear AI responses through your speakers
- **Text Chat**: View conversation history in the chat window

## License

This project is licensed under the MIT License.