# Python
__pycache__/
*.py[cod]
*$py.class
*.so
.Python
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
wheels/
*.egg-info/
.installed.cfg
*.egg
MANIFEST

# Virtual Environment
venv/
env/
ENV/
bot-env/
.venv
.venv/

# Environment variables and secrets
.env
*.env
.envmain
.envtest
config.json
secrets.json
token.txt
discord_token.txt
credentials.json

# IDE specific files
.idea/
.vscode/
*.swp
*.swo
.project
.pydevproject
.spyderproject
.spyproject
.cursor

# Logs
logs/
logs/transcriptions/
*.log
npm-debug.log*

# Windows specific
Thumbs.db
ehthumbs.db
Desktop.ini
$RECYCLE.BIN/
*.lnk

# macOS specific
.DS_Store
.AppleDouble
.LSOverride
._*

# Debug files
.coverage
htmlcov/
.pytest_cache/

# PyInstaller
*.manifest
*.spec

# Installer logs
pip-log.txt
pip-delete-this-directory.txt

# Unit test / coverage reports
.tox/
.coverage.*
.cache
nosetests.xml
coverage.xml
*.cover
.hypothesis/

# Translations
*.mo
*.pot

# Django stuff:
local_settings.py

# Flask stuff:
instance/
.webassets-cache

# Scrapy stuff:
.scrapy

# Sphinx documentation
docs/_build/

# PyBuilder
target/

# Jupyter Notebook
.ipynb_checkpoints

# pyenv
.python-version

# celery beat schedule file
celerybeat-schedule

# SageMath parsed files
*.sage.py

# Environments
env.bak/
venv.bak/

# Rope project settings
.ropeproject

# mkdocs documentation
/site

# mypy
.mypy_cache/

# Project specific
*.db
*.sqlite3

# Audio files in recordings (but keep the directory structure)
recordings/**/*.mp3
recordings/**/*.wav
recordings/**/*.txt
!recordings/bot_audio/.gitkeep
!recordings/user_audio/.gitkeep

# Cache
cache/
notes.txt