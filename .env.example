# Sidenvoice Environment Variables Example
# Copy this file to .env and fill in your values

# Flask Configuration
FLASK_SECRET_KEY=your_secret_key_here

# Google Gemini AI Configuration
GEMINI_API_KEY=your_gemini_api_key_here

# Custom TTS Configuration
# These are optional - defaults will be used if not specified
CUSTOM_TTS_MODEL=tts_models/en/ljspeech/tacotron2-DDC
CUSTOM_TTS_SAMPLE_RATE=22050

# TTS Cache Settings
TTS_CACHE_ENABLED=true
TTS_CACHE_DIR=static/audio/tts_cache
TTS_CACHE_MAX_SIZE_MB=100

# Brave Search API (Optional - for web search functionality)
BRAVE_SEARCH_API_KEY=your_brave_search_api_key_here
BRAVE_SEARCH_AI_API_KEY=your_brave_search_ai_api_key_here

# Upload Settings
UPLOAD_FOLDER=static/audio
