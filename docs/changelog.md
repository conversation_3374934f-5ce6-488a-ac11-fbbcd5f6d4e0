# Sidenvoice Changelog

This document records all significant changes to the Sidenvoice web application.

## [2024-11-15]
### Added
- Implemented custom Whisper-based speech recognition
- Removed dependency on AssemblyAI for speech-to-text
- Added local audio processing for improved privacy
- Enhanced real-time transcription with local model

### Changed
- Updated project structure to focus on web application
- Removed Discord-specific code and dependencies
- Streamlined codebase for web-only functionality

### Fixed
- Fixed audio processing in browser environment
- Improved microphone access and permissions handling
- Enhanced error handling for speech recognition

## [2024-11-10]
### Added
- Implemented web interface for AI voice interaction
- Created responsive UI with microphone button and chat display
- Added Socket.IO for real-time communication
- Integrated Gemini 2.5 Flash for AI responses

### Changed
- Updated AI system prompt for web-based interaction
- Improved conversation history management
- Enhanced text-to-speech integration for web playback

## [2024-11-05]
### Added
- Implemented ElevenLabs TTS for high-quality voice responses
- Added audio caching to reduce API calls
- Created audio file serving through Flask
- Implemented text processing utilities for TTS optimization

### Fixed
- Fixed audio playback issues in browser environment
- Improved handling of long text responses
- Enhanced error handling for TTS generation

## [2024-11-01]
### Added
- Created initial Flask web application structure
- Implemented basic Socket.IO integration
- Added configuration system for API keys and settings
- Set up project structure for web-based AI assistant

### Changed
- Migrated from Discord bot to web application architecture
- Updated documentation to reflect new project direction
- Refactored code for web-based interaction model