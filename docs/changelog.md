# VCAgentBot Changelog

This document records all significant changes to the VCAgentBot project.

## [2023-08-04]
### Added
- Enhanced voice disconnection functionality with retry logic and state verification
- Added support for disconnecting multiple users simultaneously
- Implemented contextual awareness for moderation commands
- Added transcript analysis to identify disruptive users based on conversation patterns
- Added proper name extraction from conversation context
- Refactored function_calling.py into modular structure with separate files for each handler
- Created dedicated utility functions for common operations across function handlers

### Fixed
- Fixed function declaration error with array parameters by adding required 'items' field
- Fixed issue with bot showing constant voice activity during monitoring
- Improved handling of bot's own audio to prevent feedback loops

## [2023-08-03]
### Added
- Implemented voice disconnection function calling capability for Gemini AI
- Added ability for the bot to disconnect users from voice channels for moderation purposes
- Created DiscordVoiceDisconnector class with permission checks and comprehensive error handling
- Refactored common user matching and voice channel finding code into shared utility functions
- Enhanced function calling framework with improved code organization and reusability
- Updated feature roadmap to reflect new voice moderation capability

## [2023-08-02]
### Added
- Implemented nickname change function calling capability for Gemini AI
- Added fuzzy username matching for voice channel members with minimum similarity threshold
- Created DiscordNicknameChanger class with comprehensive error handling and logging
- Enhanced function calling framework to support user nickname changes
- Updated feature roadmap to reflect new nickname change capability

## [2023-08-01]
### Added
- Implemented channel blacklisting functionality for security and privacy
- Added configurable blacklist settings in AI config for category IDs, channel IDs, and keywords
- Created security logging system for blacklist violation attempts
- Enhanced function calling to pass user IDs for security tracking
- Added comprehensive error handling for blacklisted channel access attempts

## [2023-07-31]
### Added
- Enhanced Discord message retrieval function to use channel names instead of IDs
- Implemented fuzzy matching algorithm for channel names to improve voice command usability
- Added channel name normalization to handle special characters and emojis
- Improved channel matching with scoring system based on word overlap

## [2023-07-30]
### Added
- Implemented function calling capability for Gemini AI
- Created extensible function calling framework in `src/utils/function_calling.py`
- Added Discord message retrieval function to get recent messages from channels
- Enhanced GeminiAI class to support function call detection and execution
- Updated bot initialization to register function handlers
- Added configuration options for function calling in AI config

## [2023-07-29]
### Added
- Enhanced logging for Gemini AI responses to diagnose "I couldn't generate a response" issues
- Added detailed logging of raw Gemini API response objects and their structure
- Implemented comprehensive error logging throughout the AI request pipeline
- Added specific error messages for different types of response failures
- Enhanced AI cog logging to capture full context of requests and responses

## [2023-07-28]
### Added
- Implemented `/asktext` command for text-only AI responses without TTS
- Enhanced AI command options to allow users to choose between voice and text-only responses
- Updated feature roadmap to include the new text-only response option

## [2023-07-27]
### Added
- Implemented full response TTS generation for the `/monitor` command
- Added ability to generate and play complete AI responses as a single audio file
- Enhanced TTS system to combine multiple audio chunks into a single file using ffmpeg
- Improved audio playback experience during monitoring by eliminating pauses between chunks

## [2023-07-26]
### Added
- Implemented `/stopvoice` command to stop Henry's current TTS output during long responses
- Added user feedback when stopping voice output
- Enhanced AICog with ability to interrupt ongoing TTS playback

## [2023-07-25]
### Added
- Implemented text sanitization function to remove markdown formatting from AI responses before TTS
- Created new text_processing utility module with sanitize_markdown and chunk_text_for_tts functions
- Enhanced TTS processing to ensure clean, natural speech output without markdown elements

### Fixed
- Fixed issue where markdown formatting in AI responses would be read verbally by TTS
- Improved text chunking for TTS to use more natural sentence breaks

## [2023-07-24]
### Changed
- Updated Gemini system prompt to use the new "Henry++" prompt format
- Improved AI prompt formatting to properly append requestee name, request, and transcription history
- Enhanced user identification in AI requests to better track who is making requests
- Simplified prompt structure to focus on the essential information needed by the AI

## [2023-07-23]
### Fixed
- Implemented robust Discord rate limiting handling for the /monitor command
- Added exponential backoff strategy when rate limits are encountered
- Enhanced message update logic to skip outdated updates during rate limiting
- Added user-friendly notifications when rate limiting occurs
- Fixed issue where bot would get stuck trying to process a backlog of message edits

## [2023-07-22]
### Added
- Implemented inactivity monitor to prevent bot disconnection
- Added automatic playback of keep-alive audio after 3 minutes of inactivity
- Integrated inactivity tracking with all audio playback systems
- Enhanced AI status command to show inactivity monitor status

### Fixed
- Fixed issue where bot would disconnect from voice channels after periods of inactivity

## [2023-07-21]
### Fixed
- Fixed TTS playback for longer responses by implementing text chunking
- Added sentence-aware text splitting for more natural TTS playback
- Improved TTS text truncation to find natural breakpoints
- Enhanced TTS playback reliability with sequential chunk processing

## [2023-07-20]
### Added
- Added AI responses to transcript logs
- Enhanced transcript formatting to distinguish AI responses
- Included AI responses in Discord log files
- Added special formatting for AI responses in live transcription updates

## [2023-07-19]
### Fixed
- Fixed TTS playback in voice channels
- Improved audio file handling for more reliable playback
- Added multiple fallback methods for TTS playback
- Enhanced error logging for audio playback issues
- Added support for playback from cache when API key is not available

## [2023-07-18]
### Fixed
- Updated Gemini AI integration to use the latest Google API format
- Fixed "module 'google' has no attribute 'configure'" error
- Improved error handling for Gemini API responses
- Enhanced response formatting for better compatibility with the new API

## [2023-07-17]
### Added
- Implemented AI request processing with Gemini 2.5 Flash
- Added capture of user requests following activation phrase
- Enhanced context handling to include recent conversation transcripts
- Implemented formatted Discord responses with request context
- Added support for follow-up requests after activation
- Integrated TTS for AI responses in voice channels

### Changed
- Improved system prompt for more contextual AI responses
- Enhanced response formatting for better readability in Discord
- Updated feature roadmap to mark AI request processing features as completed

## [2023-07-16]
### Added
- Implemented voice activation detection for "Hey Henry" and variations
- Added audio response playback when activation phrase is detected
- Implemented cooldown mechanism to prevent rapid repeated activations
- Connected RealtimeTranscriber to AICog for voice activation processing

### Changed
- Updated feature roadmap to mark voice activation features as completed

### Fixed
- Fixed missing connection between transcription system and AI processing