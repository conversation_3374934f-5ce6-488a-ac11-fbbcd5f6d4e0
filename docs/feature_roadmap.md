# VCAgentBot Feature Roadmap

This document outlines the planned features and their implementation status for the VCAgentBot project.

## Current Features (Implemented)

### Audio Capture and Recording
- [x] Join voice channels via `/join` command
- [x] Leave voice channels via `/leave` command
- [x] Record audio via `/record` command
- [x] Save recordings as files

### Real-time Transcription
- [x] Monitor voice channels via `/monitor` command
- [x] Real-time transcription using AssemblyAI
- [x] Per-user audio tracking and transcription
- [x] Display transcriptions in Discord text channel
- [x] Save transcription logs with speaker details
- [x] Track speaker identity (username and Discord UID)
- [x] Include timestamps in transcription logs
- [x] Implement robust Discord rate limiting handling

## Short-term Roadmap (Next Features)

### Voice Activation
- [x] Detect "<PERSON> Henry" and variations in transcribed text
- [x] Play audio response (`audio/greetings/mhm.mp3`) when activated
- [x] Implement cooldown to prevent rapid repeated activations

### AI Request Processing
- [x] Capture user request following activation phrase
- [x] Send request with recent transcript context to Gemini 2.5 Flash
- [x] Process AI response and format for Discord
- [x] Display AI response in text channel
- [x] Add `/asktext` command for text-only AI responses (no TTS)
- [x] Implement comprehensive logging for AI request pipeline and response debugging

### Text-to-Speech Response
- [x] Integrate ElevenLabs API for text-to-speech
- [x] Convert AI text responses to speech
- [x] Play speech responses in voice channel
- [x] Implement voice selection and configuration
- [x] Add caching for common responses
- [x] Sanitize markdown formatting from text before TTS processing
- [x] Add `/stopvoice` command to stop TTS playback
- [x] Generate and play full AI responses as a single audio file during monitoring

### Connection Management
- [x] Implement inactivity monitor to prevent disconnection
- [x] Play keep-alive audio after period of inactivity
- [x] Track all audio playback to reset inactivity timer

## Medium-term Roadmap

### Function Calling
- [ ] Implement web search functionality
- [x] Add Discord-specific function calls (retrieve recent messages)
- [x] Add Discord nickname change function with fuzzy username matching
- [x] Add Discord voice disconnection function for moderation
- [x] Add multi-user disconnection support with retry logic
- [x] Implement contextual awareness for moderation commands
- [x] Create extensible function calling framework
- [x] Implement result parsing and formatting
- [x] Refactor function calling code into modular structure

### Enhanced Conversation Awareness
- [x] Include AI responses in transcript logs and context
- [x] Improve context handling for more natural conversations
- [x] Enhance AI system prompt for better conversational capabilities
- [ ] Maintain longer conversation history
- [ ] Implement topic detection and summarization
- [ ] Add user preference tracking

### Voice Command System
- [ ] Create structured voice command parser
- [ ] Implement command validation and confirmation
- [ ] Add custom command definitions
- [ ] Support command chaining

## Long-term Roadmap

### Advanced AI Features
- [ ] Implement proactive assistance based on conversation context
- [ ] Add sentiment analysis for appropriate responses
- [ ] Support multiple languages for transcription and responses
- [ ] Implement voice style transfer for more natural TTS

### Integration Capabilities
- [ ] Add API for external service integration
- [ ] Support webhooks for event notifications
- [ ] Implement custom plugin system
- [ ] Create dashboard for configuration and monitoring

### Community Features
- [ ] Add user feedback mechanism
- [ ] Implement learning from corrections
- [ ] Create shared knowledge base across servers
- [ ] Support server-specific customizations

## Implementation Notes

Each feature will be implemented with the following principles in mind:
- Modularity: Each feature should be in its own module/class
- Testability: Include unit tests for core functionality
- Documentation: Clear documentation for usage and configuration
- Error handling: Robust error handling and recovery
- Performance: Optimize for low latency and resource efficiency
