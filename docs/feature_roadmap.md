# Sidenvoice Feature Roadmap

This document outlines the planned features and their implementation status for the Sidenvoice web application.

## Current Features (Implemented)

### Web Interface
- [x] Responsive web interface for desktop browsers
- [x] Audio input/output through browser
- [x] Text chat display for conversation history

### Speech Recognition
- [x] Real-time speech recognition using Whisper
- [x] Local processing without external API dependencies
- [x] Audio capture from browser microphone
- [x] Transcription with speaker identification

## Short-term Roadmap (Next Features)

### Voice Activation
- [x] Detect "Hey Henry" and variations in transcribed text
- [x] Play audio acknowledgment when activated
- [x] Implement cooldown to prevent rapid repeated activations
- [x] Support always-on listening mode

### AI Request Processing
- [x] Capture user request following activation phrase
- [x] Send request with conversation context to Gemini 2.5 Flash
- [x] Process AI response and format for web display
- [x] Display AI response in chat interface
- [x] Implement comprehensive logging for AI request pipeline

### Text-to-Speech Response
- [x] Integrate ElevenLabs API for text-to-speech
- [x] Convert AI text responses to speech
- [x] Play speech responses through browser audio
- [x] Implement voice selection and configuration
- [x] Add caching for common responses
- [x] Sanitize markdown formatting from text before TTS processing
- [x] Generate and play full AI responses as a single audio file

### User Experience
- [x] Implement responsive UI for different screen sizes
- [x] Add visual feedback during speech recognition
- [x] Provide clear indicators for AI processing state

## Medium-term Roadmap

### Function Calling
- [x] Implement web search functionality
- [x] Create extensible function calling framework
- [x] Implement result parsing and formatting
- [x] Refactor function calling code into modular structure
- [ ] Add calendar integration
- [ ] Implement weather lookup functionality
- [ ] Add note-taking capabilities

### Enhanced Conversation Awareness
- [x] Include AI responses in conversation history
- [x] Improve context handling for more natural conversations
- [x] Enhance AI system prompt for better conversational capabilities
- [ ] Maintain longer conversation history
- [ ] Implement topic detection and summarization
- [ ] Add user preference tracking

### Voice Command System
- [ ] Create structured voice command parser
- [ ] Implement command validation and confirmation
- [ ] Add custom command definitions
- [ ] Support command chaining

## Long-term Roadmap

### Advanced AI Features
- [ ] Implement proactive assistance based on conversation context
- [ ] Add sentiment analysis for appropriate responses
- [ ] Support multiple languages for transcription and responses
- [ ] Implement voice style transfer for more natural TTS

### Integration Capabilities
- [ ] Add API for external service integration
- [ ] Support webhooks for event notifications
- [ ] Implement custom plugin system
- [ ] Create dashboard for configuration and monitoring

### User Experience Enhancements
- [ ] Add user profiles and personalization
- [ ] Implement learning from user corrections
- [ ] Create shared knowledge base for common queries
- [ ] Support custom voice and appearance settings

## Implementation Notes

Each feature will be implemented with the following principles in mind:
- Modularity: Each feature should be in its own module/class
- Testability: Include unit tests for core functionality
- Documentation: Clear documentation for usage and configuration
- Error handling: Robust error handling and recovery
- Performance: Optimize for low latency and resource efficiency
