"""
Custom text-to-speech implementation using Coqui TTS.

This module provides a local TTS solution without requiring external API services.
"""

import os
import time
import hashlib
import asyncio
import logging
from typing import Optional, Dict, Any
from pathlib import Path
import numpy as np

from TTS.api import TTS
from TTS.utils.synthesizer import Synthesizer

from src.utils.text_processing import sanitize_markdown

# Set up logging
logger = logging.getLogger('sidenvoice.custom_tts')
if not logger.handlers:
    handler = logging.StreamHandler()
    formatter = logging.Formatter('%(asctime)s - %(name)s - %(levelname)s - %(message)s')
    handler.setFormatter(formatter)
    logger.addHandler(handler)
    logger.setLevel(logging.INFO)

# Default configuration
DEFAULT_MODEL = "tts_models/en/ljspeech/tacotron2-DDC"
DEFAULT_VOCODER = None  # Use the default vocoder for the model
DEFAULT_CACHE_DIR = "static/audio/tts_cache"
DEFAULT_SAMPLE_RATE = 22050


class CustomTTS:
    """Custom TTS implementation using Coqui TTS."""

    def __init__(self,
                 model_name: str = DEFAULT_MODEL,
                 vocoder_name: Optional[str] = DEFAULT_VOCODER,
                 cache_dir: str = DEFAULT_CACHE_DIR,
                 cache_enabled: bool = True,
                 sample_rate: int = DEFAULT_SAMPLE_RATE):
        """Initialize the custom TTS engine.

        Args:
            model_name: Name of the TTS model to use
            vocoder_name: Name of the vocoder to use (or None for default)
            cache_dir: Directory to store cached audio files
            cache_enabled: Whether to use caching
            sample_rate: Sample rate for audio output
        """
        self.model_name = model_name
        self.vocoder_name = vocoder_name
        self.cache_dir = cache_dir
        self.cache_enabled = cache_enabled
        self.sample_rate = sample_rate
        
        # Create cache directory if it doesn't exist
        if self.cache_enabled:
            os.makedirs(self.cache_dir, exist_ok=True)
        
        # Initialize TTS engine
        logger.info(f"Initializing Coqui TTS with model: {model_name}")
        try:
            # Use the TTS API for simpler initialization
            self.tts = TTS(model_name=model_name, vocoder_name=vocoder_name)
            logger.info("Coqui TTS initialized successfully")
        except Exception as e:
            logger.error(f"Error initializing Coqui TTS: {e}")
            raise

    async def generate_speech(self, text: str, output_path: Optional[str] = None) -> str:
        """Generate speech from text.

        Args:
            text: Text to convert to speech
            output_path: Optional path to save the audio file (if None, a path will be generated)

        Returns:
            Path to the generated audio file
        """
        # Clean the text
        clean_text = sanitize_markdown(text)
        
        # Generate a cache key based on the text
        cache_key = self._generate_cache_key(clean_text)
        cache_path = os.path.join(self.cache_dir, f"{cache_key}.wav")
        
        # If caching is enabled and the file exists, return it
        if self.cache_enabled and os.path.exists(cache_path):
            logger.info(f"Using cached audio for: {clean_text[:30]}...")
            return cache_path
        
        # Determine output path
        if output_path is None:
            output_path = cache_path
        
        # Generate speech
        try:
            logger.info(f"Generating speech for: {clean_text[:30]}...")
            
            # Run TTS in a separate thread to avoid blocking
            loop = asyncio.get_event_loop()
            await loop.run_in_executor(
                None, 
                lambda: self.tts.tts_to_file(
                    text=clean_text,
                    file_path=output_path
                )
            )
            
            logger.info(f"Speech generated successfully: {output_path}")
            return output_path
        except Exception as e:
            logger.error(f"Error generating speech: {e}")
            raise

    def _generate_cache_key(self, text: str) -> str:
        """Generate a cache key for the given text.

        Args:
            text: Text to generate a cache key for

        Returns:
            Cache key as a string
        """
        # Create a hash of the text and model name
        text_hash = hashlib.md5(f"{text}_{self.model_name}".encode()).hexdigest()
        return text_hash


class CustomTTSManager:
    """Manages TTS instances and provides a unified interface."""

    def __init__(self):
        """Initialize the TTS manager."""
        self.tts_engines: Dict[str, CustomTTS] = {}
        self.default_engine = None

    def load_model(self, model_name: str, vocoder_name: Optional[str] = None) -> CustomTTS:
        """Load a TTS model.

        Args:
            model_name: Name of the TTS model to load
            vocoder_name: Optional vocoder name

        Returns:
            CustomTTS instance
        """
        # Check if the model is already loaded
        if model_name in self.tts_engines:
            return self.tts_engines[model_name]
        
        # Load the model
        tts = CustomTTS(model_name=model_name, vocoder_name=vocoder_name)
        self.tts_engines[model_name] = tts
        
        # Set as default if this is the first model
        if self.default_engine is None:
            self.default_engine = tts
        
        return tts

    def get_engine(self, model_name: Optional[str] = None) -> CustomTTS:
        """Get a TTS engine.

        Args:
            model_name: Name of the model to use (or None for default)

        Returns:
            CustomTTS instance
        """
        if model_name is None:
            if self.default_engine is None:
                # Load the default model
                return self.load_model(DEFAULT_MODEL)
            return self.default_engine
        
        if model_name not in self.tts_engines:
            return self.load_model(model_name)
        
        return self.tts_engines[model_name]

    async def generate_speech(self, text: str, model_name: Optional[str] = None) -> str:
        """Generate speech using the specified model.

        Args:
            text: Text to convert to speech
            model_name: Name of the model to use (or None for default)

        Returns:
            Path to the generated audio file
        """
        engine = self.get_engine(model_name)
        return await engine.generate_speech(text)


# Create a singleton instance
tts_manager = CustomTTSManager()


async def generate_speech(text: str, model_name: Optional[str] = None) -> str:
    """Generate speech from text.

    Args:
        text: Text to convert to speech
        model_name: Name of the model to use (or None for default)

    Returns:
        Path to the generated audio file
    """
    return await tts_manager.generate_speech(text, model_name)


# List available models
def list_available_models() -> Dict[str, Any]:
    """List all available TTS models.

    Returns:
        Dictionary of available models
    """
    try:
        return TTS().list_models()
    except Exception as e:
        logger.error(f"Error listing models: {e}")
        return {}
