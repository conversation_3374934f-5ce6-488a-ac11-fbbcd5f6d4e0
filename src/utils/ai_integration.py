"""
AI integration for Sidenvoice using Google's Gemini AI.

This module handles:
1. Processing user requests with Gemini AI
2. Managing conversation context
3. Formatting AI responses for the web interface
4. Function calling capabilities
"""

import asyncio
import time
import json
from typing import List, Dict, Any, Optional, Union, Tuple
from google import genai
from google.genai import types

from src.config.ai_config import (
    GEMINI_API_KEY,
    GEMINI_ENABLED,
    GEMINI_MODEL,
    GEMINI_TEMPERATURE,
    GEMINI_MAX_OUTPUT_TOKENS,
    GEMINI_TOP_K,
    GEMINI_TOP_P,
    GEMINI_SYSTEM_PROMPT,
    FUNCTION_CALLING_ENABLED
)
from src.utils.function_calling import FunctionRegistry

class GeminiAI:
    """Handles integration with Google's Gemini AI."""

    def __init__(self, api_key: str = GEMINI_API_KEY):
        """Initialize the AI integration.

        Args:
            api_key: Gemini API key
        """
        self.api_key = api_key
        self.enabled = GEMINI_ENABLED
        self.model_name = GEMINI_MODEL
        self.client = None
        self.function_calling_enabled = FUNCTION_CALLING_ENABLED

        # Rate limiting
        self.last_request_time = 0
        self.min_request_interval = 1.0  # seconds

        # Initialize the API if enabled
        if self.enabled:
            try:
                # Initialize the client
                self.client = genai.Client(api_key=self.api_key)
                print(f"Gemini AI initialized with model: {self.model_name}")

                # Log function calling status
                if self.function_calling_enabled:
                    print("Function calling is enabled")
                    function_count = len(FunctionRegistry.get_all_functions())
                    print(f"Available functions: {function_count}")
                else:
                    print("Function calling is disabled")
            except Exception as e:
                print(f"Error initializing Gemini AI: {e}")
                self.enabled = False
        else:
            print("Gemini AI integration is disabled")

    async def process_request(self,
                             request_text: str,
                             conversation_context: List[Dict[str, Any]] = None,
                             user_id: int = 0) -> str:
        """Process a user request with Gemini AI.

        Args:
            request_text: The user's request text
            conversation_context: Recent conversation context
            user_id: The ID of the user making the request (for security logging)

        Returns:
            str: AI response text
        """
        print(f"=== STARTING NEW GEMINI REQUEST ===")
        print(f"Request text: {request_text}")
        print(f"Context length: {len(conversation_context) if conversation_context else 0}")

        if not self.enabled or not self.client:
            if (not self.enabled):
                print("ERROR: Gemini AI is not enabled, cannot process request")
            else:
                print("ERROR: Gemini AI client is not initialized, cannot process request")
            return "AI processing is currently unavailable."

        # Apply rate limiting
        current_time = time.time()
        time_since_last_request = current_time - self.last_request_time
        print(f"Time since last request: {time_since_last_request:.2f} seconds")

        if time_since_last_request < self.min_request_interval:
            wait_time = self.min_request_interval - time_since_last_request
            print(f"Rate limiting applied, waiting {wait_time:.2f} seconds")
            await asyncio.sleep(wait_time)

        self.last_request_time = time.time()

        # Format the prompt with context
        prompt_content = self._format_prompt(request_text, conversation_context)
        print(f"Formatted prompt (first 200 chars): {prompt_content[:200]}...")
        print(f"Total prompt length: {len(prompt_content)} characters")

        try:
            # Create generation config
            generation_config = types.GenerateContentConfig(
                temperature=GEMINI_TEMPERATURE,
                max_output_tokens=GEMINI_MAX_OUTPUT_TOKENS,
                top_k=GEMINI_TOP_K,
                top_p=GEMINI_TOP_P,
                system_instruction=GEMINI_SYSTEM_PROMPT
            )

            # Add function calling if enabled
            if self.function_calling_enabled and FunctionRegistry.get_all_functions():
                print("Adding function calling capabilities to request")

                # Get function declarations
                function_declarations = FunctionRegistry.get_function_declarations()
                print(f"Adding {len(function_declarations)} function declarations")

                # Create tools with function declarations
                tools = types.Tool(function_declarations=function_declarations)

                # Add tools to generation config
                generation_config.tools = [tools]

                # Set function calling mode to AUTO (default)
                tool_config = types.ToolConfig(
                    function_calling_config=types.FunctionCallingConfig(mode="AUTO")
                )
                generation_config.tool_config = tool_config

                print("Function calling configured for request")

            print(f"Generation config: temperature={GEMINI_TEMPERATURE}, max_tokens={GEMINI_MAX_OUTPUT_TOKENS}, top_k={GEMINI_TOP_K}, top_p={GEMINI_TOP_P}")
            print(f"System prompt length: {len(GEMINI_SYSTEM_PROMPT)} characters")
            print(f"Using model: {self.model_name}")

            print("Sending request to Gemini API...")
            request_start_time = time.time()

            # Run in executor to avoid blocking
            response = await asyncio.get_event_loop().run_in_executor(
                None,
                lambda: self.client.models.generate_content(
                    model=self.model_name,
                    contents=[prompt_content],
                    config=generation_config
                )
            )

            request_duration = time.time() - request_start_time
            print(f"Gemini API request completed in {request_duration:.2f} seconds")

            # Check for function calls
            if self.function_calling_enabled:
                has_function_call = self._check_for_function_call(response)
                if has_function_call:
                    print("Function call detected in response")
                    # Process function call and get updated response
                    response = await self._process_function_call(
                        response,
                        prompt_content,
                        generation_config,
                        user_id
                    )
                    print("Function call processing completed")

            # Extract and format the response text
            print("Formatting response...")
            response_text = self._format_response(response)

            # Log the final response text
            if response_text.startswith("I couldn't generate a response"):
                print(f"ERROR: Failed to generate valid response: {response_text}")
            else:
                print(f"Final response text (first 200 chars): {response_text[:200]}...")
                print(f"Total response length: {len(response_text)} characters")

            print(f"=== COMPLETED GEMINI REQUEST ===")
            return response_text

        except Exception as e:
            print(f"ERROR: Exception while processing request with Gemini AI: {e}")
            import traceback
            print(f"Traceback: {traceback.format_exc()}")
            print(f"=== GEMINI REQUEST FAILED WITH EXCEPTION ===")
            return f"Sorry, I encountered an error processing your request: {str(e)}"

    def _format_prompt(self,
                      request_text: str,
                      conversation_context: List[Dict[str, Any]] = None) -> str:
        """Format the prompt with context for the AI.

        Args:
            request_text: The user's request text
            conversation_context: Recent conversation context

        Returns:
            str: Formatted prompt text
        """
        # Extract requestee name from the most recent context entry or use a default
        requestee_name = "User"
        if conversation_context and len(conversation_context) > 0:
            # Find the most recent entry that matches the request
            for entry in reversed(conversation_context):
                if entry.get('text', '').strip() == request_text.strip():
                    requestee_name = entry.get('username', 'User')
                    break

        # Format the conversation context as a readable transcript
        context_text = ""
        if conversation_context:
            # Use the full conversation context without limiting
            context_lines = []
            for entry in conversation_context:
                username = entry.get('username', 'Unknown User')
                text = entry.get('text', '')
                if text:
                    context_lines.append(f"{username}: {text}")

            context_text = "\n".join(context_lines)

        # Build the final prompt with the system prompt from config
        # Just append the requestee name, request, and transcription history
        prompt = f"""
Requestee: {requestee_name}
Request: {request_text}

Recent conversation transcript:
{context_text}
"""

        return prompt

    def _check_for_function_call(self, response) -> bool:
        """Check if the response contains a function call.

        Args:
            response: Gemini response object

        Returns:
            bool: True if the response contains a function call, False otherwise
        """
        try:
            # Check if response has candidates
            if not hasattr(response, 'candidates') or not response.candidates:
                return False

            # Get the first candidate
            candidate = response.candidates[0]

            # Check if candidate has content
            if not hasattr(candidate, 'content') or not candidate.content:
                return False

            # Check if content has parts
            if not hasattr(candidate.content, 'parts') or not candidate.content.parts:
                return False

            # Check each part for a function call
            for part in candidate.content.parts:
                if hasattr(part, 'function_call') and part.function_call:
                    print(f"Found function call: {part.function_call.name}")
                    return True

            return False
        except Exception as e:
            print(f"Error checking for function call: {e}")
            return False

    async def _process_function_call(self, response, prompt_content, generation_config, user_id: int = 0):
        """Process a function call in the response.

        Args:
            response: Gemini response object
            prompt_content: The original prompt content
            generation_config: The generation config used for the request
            user_id: The ID of the user making the request (for security logging)

        Returns:
            The updated response after processing the function call
        """
        try:
            # Extract the function call
            candidate = response.candidates[0]
            function_call = None

            for part in candidate.content.parts:
                if hasattr(part, 'function_call') and part.function_call:
                    function_call = part.function_call
                    break

            if not function_call:
                print("No function call found in response")
                return response

            print(f"Processing function call: {function_call.name}")
            print(f"Function arguments: {function_call.args}")

            # Get the function handler
            function_handler = FunctionRegistry.get_function(function_call.name)
            if not function_handler:
                print(f"Function handler not found for: {function_call.name}")
                return response

            # Add user_id to function arguments for security logging if the function is get_recent_messages
            function_args = dict(function_call.args)
            if function_call.name == "get_recent_messages":
                function_args["user_id"] = str(user_id)
                print(f"Added user_id {user_id} to function arguments for security logging")

            # Execute the function
            print(f"Executing function: {function_call.name}")
            function_result = await function_handler.execute(**function_args)
            print(f"Function execution result: {function_result}")

            # Create a new conversation with the function call and result
            contents = [
                types.Content(
                    role="user",
                    parts=[types.Part(text=prompt_content)]
                ),
                types.Content(
                    role="model",
                    parts=[types.Part(function_call=function_call)]
                ),
                types.Content(
                    role="user",
                    parts=[types.Part.from_function_response(
                        name=function_call.name,
                        response={"result": function_result}
                    )]
                )
            ]

            # Send a follow-up request with the function result
            print("Sending follow-up request with function result...")
            follow_up_response = await asyncio.get_event_loop().run_in_executor(
                None,
                lambda: self.client.models.generate_content(
                    model=self.model_name,
                    contents=contents,
                    config=generation_config
                )
            )

            print("Received follow-up response")
            return follow_up_response

        except Exception as e:
            print(f"Error processing function call: {e}")
            import traceback
            print(f"Traceback: {traceback.format_exc()}")
            return response

    def _format_response(self, response) -> str:
        """Format the AI response for Discord.

        Args:
            response: Gemini response object

        Returns:
            str: Formatted response text
        """
        # Log the raw response object for debugging
        print(f"Raw Gemini response object: {response}")

        # Log the raw JSON representation of the response
        try:
            import json
            response_json = response.model_dump()
            print(f"Raw Gemini response JSON: {json.dumps(response_json, indent=2)}")
        except Exception as json_e:
            print(f"Error dumping response to JSON: {json_e}")
            try:
                # Alternative method to get JSON
                print(f"Trying alternative JSON dump method...")
                if hasattr(response, 'model_dump_json'):
                    response_json_str = response.model_dump_json()
                    print(f"Raw Gemini response JSON (alt method): {response_json_str}")
            except Exception as alt_json_e:
                print(f"Error with alternative JSON dump: {alt_json_e}")

        # Try to log the response type and structure
        try:
            print(f"Response type: {type(response)}")
            print(f"Response dir: {dir(response)}")

            # Log if response has text attribute
            has_text = hasattr(response, 'text')
            print(f"Response has text attribute: {has_text}")

            if has_text:
                print(f"Response text type: {type(response.text)}")
                print(f"Response text length: {len(response.text) if response.text else 0}")
                print(f"Response text preview: {response.text[:100] if response.text else 'None'}")

            # Log if response has candidates
            has_candidates = hasattr(response, 'candidates')
            print(f"Response has candidates: {has_candidates}")

            if has_candidates:
                print(f"Number of candidates: {len(response.candidates)}")
                if response.candidates:
                    print(f"First candidate type: {type(response.candidates[0])}")
                    print(f"First candidate dir: {dir(response.candidates[0])}")

                    # Log candidate details
                    candidate = response.candidates[0]
                    print(f"Candidate finish_reason: {candidate.finish_reason}")

                    if hasattr(candidate, 'content') and candidate.content:
                        print(f"Candidate content: {candidate.content}")
                        if hasattr(candidate.content, 'parts') and candidate.content.parts:
                            for i, part in enumerate(candidate.content.parts):
                                print(f"Content part {i}: {part}")
                                if hasattr(part, 'text'):
                                    print(f"Part {i} text: '{part.text}'")
                                # Check for function call
                                if hasattr(part, 'function_call') and part.function_call:
                                    print(f"Part {i} has function call: {part.function_call.name}")
        except Exception as log_e:
            print(f"Error logging response details: {log_e}")

        if not response:
            print("ERROR: Empty response object received from Gemini")
            return "I couldn't generate a response. (Empty response object)"

        if not hasattr(response, 'text'):
            print("ERROR: Response object has no 'text' attribute")
            return "I couldn't generate a response. (No text attribute)"

        if not response.text:
            print("ERROR: Response text is None or empty")

            # Try to get the finish reason from the candidate
            finish_reason = "unknown"
            try:
                if hasattr(response, 'candidates') and response.candidates:
                    candidate = response.candidates[0]
                    if hasattr(candidate, 'finish_reason') and candidate.finish_reason:
                        finish_reason = str(candidate.finish_reason)
                        print(f"Finish reason: {finish_reason}")
            except Exception as fr_e:
                print(f"Error getting finish reason: {fr_e}")

            return f"I couldn't generate a response. (Empty text, finish_reason: {finish_reason})"

        if not response.text.strip():
            print("ERROR: Response text contains only whitespace")
            return "I couldn't generate a response. (Whitespace only text)"

        try:
            # Get the raw text from the response
            text = response.text

            # Log the successful text extraction
            print(f"Successfully extracted text from response: {text[:100]}...")

            # Ensure it's not too long for Discord
            if len(text) > 1900:  # Leave some room for formatting
                text = text[:1900] + "..."

            return text
        except Exception as e:
            print(f"Error formatting Gemini response: {e}")
            print(f"Response object at time of error: {response}")

            # Try alternative response format access methods
            try:
                print("Attempting alternative response format access...")
                # Try accessing the first candidate's content
                if hasattr(response, 'candidates') and response.candidates:
                    candidate = response.candidates[0]
                    print(f"Found candidate: {candidate}")

                    if hasattr(candidate, 'content') and candidate.content:
                        parts = candidate.content.parts
                        print(f"Found content parts: {parts}")

                        if parts:
                            text = parts[0].text
                            print(f"Successfully extracted text from parts: {text[:100]}...")

                            if len(text) > 1900:
                                text = text[:1900] + "..."
                            return text
                        else:
                            print("ERROR: Content parts list is empty")
                    else:
                        print("ERROR: Candidate has no content attribute or content is None")
                else:
                    print("ERROR: Response has no candidates attribute or candidates list is empty")
            except Exception as inner_e:
                print(f"Error accessing alternative response format: {inner_e}")
                print(f"Response object during alternative access: {response}")

            return "I received a response but couldn't format it properly."
