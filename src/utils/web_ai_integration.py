"""
AI integration for Sidenvoice web application using Google's Gemini 2.5 Flash.

This module handles:
1. Processing user requests with Gemini AI
2. Managing conversation context
3. Formatting AI responses for web display
"""

import asyncio
import time
import json
import re
from typing import List, Dict, Any, Optional, Union, Tuple
from google import genai
from google.genai import types

from src.config.ai_config import (
    GEMINI_API_KEY,
    GEMINI_ENABLED,
    GEMINI_MODEL,
    GEMINI_TEMPERATURE,
    GEMINI_MAX_OUTPUT_TOKENS,
    GEMINI_TOP_K,
    GEMINI_TOP_P,
    GEMINI_SYSTEM_PROMPT,
    BRAVE_SEARCH_API_KEY,
    BRAVE_SEARCH_AI_API_KEY,
    <PERSON><PERSON><PERSON>_SEARCH_ENABLED
)

# Import Brave Search functionality
from src.utils.brave_search import BraveWebTool

class WebGeminiAI:
    """Handles integration with Google's Gemini 2.5 Flash AI for web application."""

    def __init__(self, api_key: str = GEMINI_API_KEY):
        """Initialize the AI integration.

        Args:
            api_key: Gemini API key
        """
        self.api_key = api_key
        self.enabled = GEMINI_ENABLED
        self.model_name = GEMINI_MODEL
        self.client = None

        # Rate limiting
        self.last_request_time = 0
        self.min_request_interval = 1.0  # seconds

        # Initialize web search tool if enabled
        self.brave_search = None
        if BRAVE_SEARCH_ENABLED:
            try:
                self.brave_search = BraveWebTool(
                    api_key=BRAVE_SEARCH_API_KEY,
                    ai_api_key=BRAVE_SEARCH_AI_API_KEY
                )
                print("Brave Search API initialized for web search capabilities")
            except Exception as e:
                print(f"Warning: Failed to initialize Brave Search API: {e}")

        # Initialize the AI API if enabled
        if self.enabled:
            try:
                # Initialize the client
                self.client = genai.Client(api_key=self.api_key)
                print(f"Gemini AI initialized with model: {self.model_name}")
            except Exception as e:
                print(f"Error initializing Gemini AI: {e}")
                self.enabled = False
        else:
            print("Gemini AI integration is disabled")

    async def process_request(self, user_input: str, conversation_history: List[Dict[str, Any]] = None) -> Dict[str, Any]:
        """Process a user request and generate an AI response.

        Args:
            user_input: The user's input text
            conversation_history: Previous conversation messages

        Returns:
            Dict containing the AI response and metadata
        """
        if not self.enabled or not self.client:
            return {
                "success": False,
                "response": "AI integration is not enabled or properly configured.",
                "error": "AI service unavailable"
            }

        # Check if this is a web search request
        web_search_needed = self._needs_web_search(user_input)
        web_search_results = None

        # Perform web search if needed and available
        if web_search_needed and self.brave_search:
            try:
                search_query = self._extract_search_query(user_input)
                web_search_results = await self._perform_web_search(search_query)
            except Exception as e:
                print(f"Error performing web search: {e}")

        try:
            # Enforce rate limiting
            current_time = time.time()
            time_since_last_request = current_time - self.last_request_time
            if time_since_last_request < self.min_request_interval:
                await asyncio.sleep(self.min_request_interval - time_since_last_request)

            # Update last request time
            self.last_request_time = time.time()

            # Prepare conversation history
            messages = []

            # Add system prompt
            messages.append({
                "role": "system",
                "parts": [GEMINI_SYSTEM_PROMPT]
            })

            # Add web search results if available
            if web_search_results:
                search_context = self._format_search_results(web_search_results)
                messages.append({
                    "role": "system",
                    "parts": [f"IMPORTANT - Web search results for user query:\n\n{search_context}\n\nYou MUST incorporate these search results into your response. Always prioritize this current information over your training data. Clearly indicate when you're using information from these search results. If the search results contain information about versions, dates, or recent developments, make sure to highlight this in your response."]
                })

            # Generate response
            response = await self._generate_response(messages, user_input)

            return {
                "success": True,
                "response": response,
                "error": None,
                "web_search_performed": web_search_needed and web_search_results is not None,
                "web_search_results": web_search_results if web_search_results else None
            }
        except Exception as e:
            print(f"Error processing request: {e}")
            return {
                "success": False,
                "response": "An error occurred while processing your request.",
                "error": str(e)
            }

    async def _generate_response(self, messages: List[Dict[str, Any]], user_input: str) -> str:
        """Generate a response using the Gemini AI model.

        Args:
            messages: Conversation history and context
            user_input: User's input text

        Returns:
            str: AI response text
        """
        try:
            # Create generation config
            generation_config = types.GenerateContentConfig(
                temperature=GEMINI_TEMPERATURE,
                max_output_tokens=GEMINI_MAX_OUTPUT_TOKENS,
                top_k=GEMINI_TOP_K,
                top_p=GEMINI_TOP_P
            )

            # Format the prompt with system instructions and context
            prompt = ""

            # Add system messages
            for message in messages:
                if message.get("role") == "system":
                    for part in message.get("parts", []):
                        prompt += part + "\n\n"

            # Add the user's input
            prompt += f"User: {user_input}\n\nResponse:"

            # Run in executor to avoid blocking
            response = await asyncio.get_event_loop().run_in_executor(
                None,
                lambda: self.client.models.generate_content(
                    model=self.model_name,
                    contents=prompt,
                    config=generation_config
                )
            )

            # Extract and format the response text
            response_text = self._format_response(response)

            return response_text
        except Exception as e:
            print(f"Error generating response: {e}")
            return "An error occurred while generating a response."

    def _format_response(self, response) -> str:
        """Format the AI response for web display.

        Args:
            response: Gemini response object

        Returns:
            str: Formatted response text
        """
        if not response:
            print("ERROR: Empty response object received from Gemini")
            return "I couldn't generate a response. (Empty response object)"

        try:
            # Get the raw text from the response
            text = response.text

            # Log the successful text extraction
            print(f"Successfully extracted text from response: {text[:100]}...")
            return text

        except Exception as e:
            print(f"Error formatting Gemini response: {e}")

            # Try alternative response format access methods
            try:
                print("Attempting alternative response format access...")
                # Try accessing the first candidate's content
                if hasattr(response, 'candidates') and response.candidates:
                    candidate = response.candidates[0]

                    if hasattr(candidate, 'content') and candidate.content:
                        parts = candidate.content.parts

                        if parts:
                            text = parts[0].text
                            print(f"Successfully extracted text from parts: {text[:100]}...")
                            return text
            except Exception as inner_e:
                print(f"Error accessing alternative response format: {inner_e}")

            return "I received a response but couldn't format it properly."

    def _needs_web_search(self, user_input: str) -> bool:
        """Determine if the user input requires a web search.

        Args:
            user_input: The user's input text

        Returns:
            True if web search is needed, False otherwise
        """
        # Always perform web search for most queries to ensure up-to-date information
        # Only skip web search for very simple, conversational queries

        # Skip patterns - simple conversational queries that don't need web search
        skip_patterns = [
            r"^hi+$", r"^hello+$", r"^hey+$", r"^thanks+$", r"^thank you+$",
            r"^how are you+$", r"^good morning+$", r"^good afternoon+$", r"^good evening+$",
            r"^bye+$", r"^goodbye+$", r"^see you+$"
        ]

        # Check if the query matches any skip patterns
        for pattern in skip_patterns:
            if re.match(pattern, user_input.strip().lower()):
                return False

        # Keywords that definitely suggest a web search is needed
        search_indicators = [
            r"look up", r"search for", r"find information", r"research",
            r"what is", r"who is", r"tell me about", r"latest news",
            r"recent", r"current", r"tweet", r"social media", r"update",
            r"information on", r"details about", r"learn about",
            r"new", r"version", r"release", r"update", r"latest", r"claude", r"ai", r"model",
            r"when", r"how", r"why", r"where", r"which", r"what", r"who",
            r"feature", r"capability", r"announcement", r"news", r"today", r"yesterday",
            r"this week", r"this month", r"this year"
        ]

        # Check if any search indicators are in the user input
        for indicator in search_indicators:
            if re.search(indicator, user_input, re.IGNORECASE):
                return True

        # Default to performing a web search for most queries
        # This ensures we get the most up-to-date information
        return len(user_input.split()) > 3

    def _extract_search_query(self, user_input: str) -> str:
        """Extract the search query from the user input.

        Args:
            user_input: The user's input text

        Returns:
            The extracted search query
        """
        # Remove common phrases that aren't part of the actual search query
        prefixes_to_remove = [
            r"^can you look up", r"^look up", r"^search for", r"^find information about",
            r"^research", r"^tell me about", r"^what is", r"^who is", r"^find out about",
            r"^please tell me", r"^i want to know", r"^i'd like to know", r"^i would like to know",
            r"^do you know", r"^could you tell me", r"^can you tell me"
        ]

        query = user_input
        for prefix in prefixes_to_remove:
            query = re.sub(prefix, "", query, flags=re.IGNORECASE).strip()

        # Remove question marks and other punctuation at the end
        query = re.sub(r"[?!.]+$", "", query).strip()

        # Add specific keywords for certain types of queries
        if re.search(r"claude", query, re.IGNORECASE) and re.search(r"(new|latest|recent|version)", query, re.IGNORECASE):
            query = "latest Claude AI model version capabilities" + " " + query

        # Add "latest" for queries about versions or updates
        if re.search(r"(version|update|release)", query, re.IGNORECASE) and not re.search(r"(latest|newest|recent)", query, re.IGNORECASE):
            query = "latest " + query

        # Add current year for relevance
        current_year = 2025
        if not re.search(str(current_year), query):
            query = query + " " + str(current_year)

        return query

    async def _perform_web_search(self, search_query: str, num_results: int = 5) -> List[Dict[str, Any]]:
        """Perform a web search using the Brave Search API.

        Args:
            search_query: The search query
            num_results: Number of results to return

        Returns:
            List of search results
        """
        if not self.brave_search:
            return []

        try:
            # Perform the search
            result = await self.brave_search.brave_web_search(query=search_query, num_results=num_results)

            if result.success and result.data:
                return result.data
            else:
                print(f"Web search failed: {result.error}")
                return []

        except Exception as e:
            print(f"Error in web search: {e}")
            return []

    def _format_search_results(self, results: List[Dict[str, Any]]) -> str:
        """Format search results into a readable string.

        Args:
            results: List of search results

        Returns:
            Formatted search results string
        """
        if not results:
            return "No relevant search results found."

        # Add current date/time to emphasize freshness of results
        import datetime
        utc_date = datetime.datetime.now(datetime.timezone.utc).strftime('%Y-%m-%d')
        utc_time = datetime.datetime.now(datetime.timezone.utc).strftime('%H:%M:%S')
        formatted_results = f"### Search Results (Retrieved: UTC DATE: {utc_date}, UTC TIME: {utc_time})\n\n"
        formatted_results += "IMPORTANT: These are current search results from the web. Use this information to provide the most up-to-date response.\n\n"

        for i, result in enumerate(results, 1):
            title = result.get("title", "Untitled")
            url = result.get("url", "")
            description = result.get("description", "No description available.")
            # Extract any date information from the title or description
            date_match = re.search(r'\b(20\d\d|Jan|Feb|Mar|Apr|May|Jun|Jul|Aug|Sep|Oct|Nov|Dec)\b', title + " " + description)
            date_info = f" [Date mentioned: {date_match.group(0)}]" if date_match else ""

            formatted_results += f"**{i}. {title}**{date_info}\n"
            formatted_results += f"URL: {url}\n"
            formatted_results += f"Description: {description}\n\n"

        formatted_results += "Remember to incorporate this current information into your response and clearly indicate when you're using information from these search results.\n"
        return formatted_results
