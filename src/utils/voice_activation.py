"""
Voice activation detection for Sidenvoice web application.

This module handles:
1. Detecting activation phrases in transcribed text
2. Extracting user requests from transcribed text
"""

import os
import re
import time
from typing import Tuple, List, Optional, Dict, Any

# Default activation phrases
DEFAULT_ACTIVATION_PHRASES = ["hey henry", "hi henry", "hello henry"]
DEFAULT_COOLDOWN = 3.0  # seconds

class VoiceActivationDetector:
    """Detects voice activation phrases in transcribed text."""

    def __init__(self,
                 activation_phrases: List[str] = None,
                 cooldown_seconds: float = DEFAULT_COOLDOWN):
        """Initialize the detector.

        Args:
            activation_phrases: List of phrases that trigger activation
            cooldown_seconds: Cooldown period between activations (seconds)
        """
        self.activation_phrases = activation_phrases or DEFAULT_ACTIVATION_PHRASES
        self.cooldown_seconds = cooldown_seconds
        self.last_activation_time = 0

        # Sort phrases by length (descending) to match longest phrases first
        self.activation_phrases.sort(key=len, reverse=True)

        # Compile regex patterns for each activation phrase
        self.patterns = [
            re.compile(rf'\b{re.escape(phrase)}\b', re.IGNORECASE)
            for phrase in self.activation_phrases
        ]

        print(f"Voice activation detector initialized with {len(self.activation_phrases)} phrases")

    def detect_activation(self,
                         text: str,
                         current_time: float = None) -> Tuple[bool, Optional[str], Optional[str]]:
        """Check if the text contains an activation phrase.

        Args:
            text: The text to check
            current_time: Current time in seconds since epoch (for testing)

        Returns:
            Tuple of (is_activated, activation_phrase, remaining_text)
        """
        current_time = current_time or time.time()
        
        # Check cooldown
        if current_time - self.last_activation_time < self.cooldown_seconds:
            return False, None, None

        # Check for activation phrases
        for i, pattern in enumerate(self.patterns):
            match = pattern.search(text)
            if match:
                activation_phrase = self.activation_phrases[i]
                remaining_text = text[:match.start()] + text[match.end():].strip()
                self.last_activation_time = current_time
                return True, activation_phrase, remaining_text

        return False, None, None

    def get_cooldown_remaining(self, current_time: float = None) -> float:
        """Get the remaining cooldown time in seconds.

        Args:
            current_time: Current time (defaults to time.time())

        Returns:
            float: Remaining cooldown time in seconds (0 if cooldown expired)
        """
        if current_time is None:
            current_time = time.time()

        elapsed = current_time - self.last_activation_time
        remaining = max(0, self.cooldown_seconds - elapsed)

        return remaining
