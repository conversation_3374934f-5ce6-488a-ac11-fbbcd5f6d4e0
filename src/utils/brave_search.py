"""
Brave Search API integration for Sidenvoice.

This module provides functionality to perform web searches and scrape web pages
using the Brave Search API.
"""

import httpx
import os
import json
from typing import List, Dict, Any, Optional
from dataclasses import dataclass

# Import configuration
from src.config.ai_config import (
    BRAVE_SEARCH_API_KEY,
    BRAVE_SEARCH_AI_API_KEY
)

@dataclass
class SearchResult:
    """Container for search results."""
    success: bool
    data: Any = None
    error: str = None


class BraveWebTool:
    """Tool for performing web searches and web scraping using Brave API."""

    def __init__(self, api_key: str = None, ai_api_key: str = None):
        """Initialize the Brave Web Search client.
        
        Args:
            api_key: Brave Search API key
            ai_api_key: Brave AI API key (optional, for enhanced features)
        """
        # Use the provided API keys or get them from configuration
        self.brave_api_key = api_key or BRAVE_SEARCH_API_KEY
        self.brave_ai_api_key = ai_api_key or BRAVE_SEARCH_AI_API_KEY
        
        if not self.brave_api_key:
            raise ValueError("Brave Search API key is required")

    async def brave_web_search(
        self, 
        query: str, 
        num_results: int = 10
    ) -> SearchResult:
        """Search the web for up-to-date information on a specific topic using the Brave Search API.
        
        This tool allows you to gather real-time information from the internet to answer user queries,
        research topics, validate facts, and find recent developments.
        
        Args:
            query: The search query to find information about
            num_results: The number of search results to return (default: 10, max: 20)
            
        Returns:
            SearchResult containing the search results or error information
        """
        try:
            # Ensure we have a valid query
            if not query or not isinstance(query, str):
                return SearchResult(success=False, error="A valid search query is required.")
            
            # Normalize num_results
            if num_results is None:
                num_results = 10
            elif isinstance(num_results, int):
                num_results = max(1, min(num_results, 20))
            elif isinstance(num_results, str):
                try:
                    num_results = max(1, min(int(num_results), 20))
                except ValueError:
                    num_results = 10
            else:
                num_results = 10

            # Execute the search with Brave API
            async with httpx.AsyncClient() as client:
                headers = {
                    "Accept": "application/json",
                    "Accept-Encoding": "gzip",
                    "X-Subscription-Token": self.brave_api_key
                }
                
                params = {
                    "q": query,
                    "count": num_results
                }
                
                response = await client.get(
                    "https://api.search.brave.com/res/v1/web/search",
                    params=params,
                    headers=headers,
                    timeout=30
                )
                
                response.raise_for_status()
                search_response = response.json()

            # Format results consistently
            formatted_results = []
            
            if "web" in search_response and "results" in search_response["web"]:
                for result in search_response["web"]["results"]:
                    formatted_result = {
                        "title": result.get("title", ""),
                        "url": result.get("url", ""),
                        "description": result.get("description", "")
                    }
                    formatted_results.append(formatted_result)
            
            return SearchResult(success=True, data=formatted_results)
            
        except httpx.HTTPStatusError as e:
            return SearchResult(success=False, error=f"HTTP error occurred: {e.response.status_code} - {e.response.text}")
        except httpx.RequestError as e:
            return SearchResult(success=False, error=f"Request error occurred: {str(e)}")
        except Exception as e:
            return SearchResult(success=False, error=f"An error occurred: {str(e)}")

    async def brave_scrape_webpage(
        self,
        url: str
    ) -> SearchResult:
        """Retrieve the complete text content of a specific webpage using Brave API.
        
        This tool extracts the full text content from any accessible web page and returns it
        for analysis, processing, or reference. The extracted text includes the main content
        of the page without HTML markup.
        
        Args:
            url: The URL of the webpage to scrape
            
        Returns:
            SearchResult containing the scraped content or error information
        """
        try:
            # Ensure we have a valid URL
            if not url or not isinstance(url, str):
                return SearchResult(success=False, error="A valid URL is required.")
            
            # Use Brave Search API to get the content
            async with httpx.AsyncClient() as client:
                headers = {
                    "Accept": "application/json",
                    "Accept-Encoding": "gzip",
                    "X-Subscription-Token": self.brave_api_key
                }
                
                # First, try to get the content using the web search API with the URL as the query
                params = {
                    "q": f"url:{url}",
                    "count": 1
                }
                
                response = await client.get(
                    "https://api.search.brave.com/res/v1/web/search",
                    params=params,
                    headers=headers,
                    timeout=30
                )
                
                response.raise_for_status()
                search_response = response.json()
                
                # If we have AI API key, use the summarizer endpoint for better content extraction
                if self.brave_ai_api_key:
                    ai_headers = {
                        "Accept": "application/json",
                        "Accept-Encoding": "gzip",
                        "X-Subscription-Token": self.brave_ai_api_key
                    }
                    
                    ai_params = {
                        "q": f"url:{url}",
                        "search_response": json.dumps(search_response)
                    }
                    
                    ai_response = await client.get(
                        "https://api.search.brave.com/res/v1/summarizer/search",
                        params=ai_params,
                        headers=ai_headers,
                        timeout=60
                    )
                    
                    if ai_response.status_code == 200:
                        ai_data = ai_response.json()
                        if "summary" in ai_data:
                            # Format the response with AI-enhanced content
                            formatted_result = {
                                "title": search_response.get("query", {}).get("original_query", url),
                                "url": url,
                                "content": ai_data.get("summary", ""),
                                "source": "Brave AI API"
                            }
                            return SearchResult(success=True, data=[formatted_result])
            
            # If we don't have AI API key or AI request failed, use the web search results
            content = ""
            title = url
            
            if "web" in search_response and "results" in search_response["web"] and search_response["web"]["results"]:
                result = search_response["web"]["results"][0]
                title = result.get("title", url)
                content = result.get("description", "")
                
                # If there's additional content in the result, add it
                if "extra_snippets" in result:
                    content += "\n\n" + "\n".join(result.get("extra_snippets", []))
            
            # Format the response
            formatted_result = {
                "title": title,
                "url": url,
                "content": content,
                "source": "Brave Search API"
            }
            
            return SearchResult(success=True, data=[formatted_result])
            
        except httpx.HTTPStatusError as e:
            return SearchResult(success=False, error=f"HTTP error occurred: {e.response.status_code} - {e.response.text}")
        except httpx.RequestError as e:
            return SearchResult(success=False, error=f"Request error occurred: {str(e)}")
        except Exception as e:
            return SearchResult(success=False, error=f"An error occurred: {str(e)}")


if __name__ == "__main__":
    import asyncio
    
    async def test_brave_web_search():
        """Test function for the Brave web search tool"""
        search_tool = BraveWebTool()
        result = await search_tool.brave_web_search(
            query="latest AI developments",
            num_results=5
        )
        print(f"Search success: {result.success}")
        if result.success:
            print(f"Found {len(result.data)} results")
            for i, item in enumerate(result.data, 1):
                print(f"\n{i}. {item['title']}")
                print(f"   URL: {item['url']}")
                print(f"   {item['description'][:100]}...")
        else:
            print(f"Search failed: {result.error}")
    
    async def test_brave_scrape_webpage():
        """Test function for the Brave webpage scrape tool"""
        search_tool = BraveWebTool()
        result = await search_tool.brave_scrape_webpage(
            url="https://www.wired.com/story/anthropic-benevolent-artificial-intelligence/"
        )
        print(f"Scrape success: {result.success}")
        if result.success and result.data:
            print(f"Title: {result.data[0]['title']}")
            print(f"URL: {result.data[0]['url']}")
            print(f"Content preview: {result.data[0]['content'][:200]}...")
        else:
            print(f"Scrape failed: {result.error}")
    
    async def run_tests():
        """Run all test functions"""
        await test_brave_web_search()
        await test_brave_scrape_webpage()
        
    asyncio.run(run_tests())
