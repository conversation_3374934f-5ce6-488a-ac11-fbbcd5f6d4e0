import io
import assemblyai as aai
from typing import Dict, List, Tuple

# Import the configuration to check if transcription is enabled
from src.config.config import ASSEMBLYAI_ENABLED, ASSEMBLYAI_API_KEY

async def perform_transcription(audio_streams: Dict[int, io.BytesIO]) -> Tuple[str, List[str]]:
    """Transcribes audio streams using AssemblyAI.

    Args:
        audio_streams: A dictionary mapping user_id (int) to their audio data (BytesIO).

    Returns:
        A tuple containing:
        - The formatted transcription message string for Discord.
        - A list of individual transcription results (for logging/debugging).
    """
    transcription_results: List[str] = []
    transcription_message: str = ""

    if not ASSEMBLYAI_ENABLED:
        transcription_message = "\n\nTranscription skipped: AssemblyAI is not configured or enabled."
        return transcription_message, transcription_results # Return early

    if not audio_streams:
        transcription_message = "\n\nNo audio data captured for transcription."
        return transcription_message, transcription_results # Return early

    # If enabled and streams exist, proceed with transcription
    transcriber = aai.Transcriber()
    print(f"Starting transcription for {len(audio_streams)} audio file(s)...")

    for user_id, audio_stream in audio_streams.items():
        try:
            # Ensure stream is at the beginning (important if stream was read before)
            audio_stream.seek(0)

            # Transcribe the audio stream
            transcript: aai.Transcript = transcriber.transcribe(audio_stream)

            # Process transcription results
            if transcript.status == aai.TranscriptStatus.error:
                result = f"<@{user_id}>: Transcription failed - {transcript.error}"
                transcription_results.append(result)
                print(f"Transcription error for {user_id}: {transcript.error}")
            elif transcript.text:
                result = f"<@{user_id}>: {transcript.text}"
                transcription_results.append(result)
                print(f"Transcription success for {user_id}.")
            else:
                # Handle cases like completed but no speech, or unexpected statuses
                if transcript.status == aai.TranscriptStatus.completed:
                    result = f"<@{user_id}>: (No speech detected)"
                    transcription_results.append(result)
                    print(f"Transcription complete for {user_id}, but no speech detected.")
                else:
                    result = f"<@{user_id}>: Transcription status uncertain - {transcript.status}"
                    transcription_results.append(result)
                    print(f"Unexpected transcription status for {user_id}: {transcript.status}")

        except Exception as e:
            print(f"Error during transcription API call for user {user_id}: {e}")
            transcription_results.append(f"<@{user_id}>: Transcription error (API call failed).")
        finally:
            # Close the stream associated with this user as it's processed
            audio_stream.close()

    # Construct the final transcription message part based on results
    if transcription_results:
        transcription_details = "\n".join(transcription_results)
        transcription_message = f"\n\n**Transcriptions:**\n{transcription_details}"
    else:
        # If no results were generated despite trying
        transcription_message = "\n\nNo speech detected in the recordings."

    return transcription_message, transcription_results 