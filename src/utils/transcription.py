import io
from typing import Dict, List, <PERSON><PERSON>

# Import the configuration to check if transcription is enabled
from src.config.config import WHISPER_ENABLED, WHISPER_MODEL_SIZE
from src.utils.whisper_transcriber import WhisperBatchTranscriber

# Create a global instance of the batch transcriber
_batch_transcriber = None

def get_batch_transcriber() -> WhisperBatchTranscriber:
    """Get or create the batch transcriber instance."""
    global _batch_transcriber
    if _batch_transcriber is None:
        _batch_transcriber = WhisperBatchTranscriber(model_size=WHISPER_MODEL_SIZE)
    return _batch_transcriber

async def perform_transcription(audio_streams: Dict[int, io.BytesIO]) -> Tuple[str, List[str]]:
    """Transcribes audio streams using Whisper.

    Args:
        audio_streams: A dictionary mapping user_id (int) to their audio data (BytesIO).

    Returns:
        A tuple containing:
        - The formatted transcription message string.
        - A list of individual transcription results (for logging/debugging).
    """
    if not WHISPER_ENABLED:
        transcription_message = "\n\nTranscription skipped: Speech recognition is not enabled."
        return transcription_message, []

    if not audio_streams:
        transcription_message = "\n\nNo audio data captured for transcription."
        return transcription_message, []

    # Get the batch transcriber
    transcriber = get_batch_transcriber()

    # Perform the transcription
    return await transcriber.transcribe(audio_streams)