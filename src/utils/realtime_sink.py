"""
Audio stream adapter for real-time speech recognition.

This module provides a class that adapts audio data for streaming to speech recognition engines.
"""

import queue
import threading
import time
from typing import Optional


class AudioStreamAdapter:
    """
    Adapts audio data for streaming to speech recognition engines.
    
    This class provides a buffer for audio data that can be read by speech recognition
    engines in a streaming fashion. It implements the necessary methods to be used
    as a stream source.
    """
    
    def __init__(self, sample_rate: int = 16000, chunk_size: int = 600):
        """Initialize the audio stream adapter.
        
        Args:
            sample_rate: Audio sample rate in Hz
            chunk_size: Size of audio chunks to read in milliseconds
        """
        self.sample_rate = sample_rate
        self.chunk_size = chunk_size
        
        # Calculate bytes per chunk based on sample rate and chunk size
        # 16-bit audio = 2 bytes per sample
        self.bytes_per_chunk = int((sample_rate * 2 * chunk_size) / 1000)
        
        # Audio buffer queue
        self.audio_queue = queue.Queue()
        
        # State tracking
        self.closed = False
        self.buffer = bytearray()
        
    def add_audio(self, audio_data: bytes):
        """Add audio data to the stream.
        
        Args:
            audio_data: Raw audio bytes to add to the stream
        """
        if self.closed:
            return
            
        self.audio_queue.put(audio_data)
    
    def read(self, size: int = -1) -> bytes:
        """Read audio data from the stream.
        
        This method is called by speech recognition engines to get audio data.
        It will block until enough data is available or the stream is closed.
        
        Args:
            size: Number of bytes to read, or -1 to read all available data
                
        Returns:
            Audio data as bytes
        """
        if self.closed:
            return b''
            
        # If size is -1, return all available data
        if size == -1:
            # Get all data from the queue
            while not self.audio_queue.empty():
                self.buffer.extend(self.audio_queue.get())
            
            # Return the buffer and clear it
            data = bytes(self.buffer)
            self.buffer.clear()
            return data
            
        # If we already have enough data in the buffer, return it
        if len(self.buffer) >= size:
            data = bytes(self.buffer[:size])
            self.buffer = self.buffer[size:]
            return data
            
        # Otherwise, read from the queue until we have enough data
        while len(self.buffer) < size:
            try:
                # Wait for data with a timeout
                chunk = self.audio_queue.get(timeout=0.5)
                self.buffer.extend(chunk)
            except queue.Empty:
                # If we timeout and have some data, return what we have
                if self.buffer:
                    break
                    
                # If we're closed, return empty
                if self.closed:
                    return b''
                    
                # Otherwise, keep waiting
                continue
                
        # Return up to size bytes
        data_size = min(size, len(self.buffer))
        data = bytes(self.buffer[:data_size])
        self.buffer = self.buffer[data_size:]
        return data
    
    def close(self):
        """Close the stream."""
        self.closed = True
