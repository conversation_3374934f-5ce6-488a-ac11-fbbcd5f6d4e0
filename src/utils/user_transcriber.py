import threading
import queue
import assemblyai as aai
import time
import datetime
from typing import Dict, Any, Optional, Callable

class UserTranscriber:
    """Handles real-time transcription for a single user in a Discord voice channel."""

    def __init__(self,
                 user_id: int,
                 username: str,
                 sample_rate: int = 16000,
                 on_transcript: Optional[Callable[[Dict[str, Any]], None]] = None):
        """Initialize the user transcriber.

        Args:
            user_id: Discord user ID
            username: Discord username for display
            sample_rate: Audio sample rate
            on_transcript: Callback function for new transcripts
        """
        self.user_id = user_id
        self.username = username
        self.sample_rate = sample_rate
        self.on_transcript = on_transcript

        # Transcription components
        self.transcriber: Optional[aai.RealtimeTranscriber] = None
        self.audio_stream: Optional[AudioStreamAdapter] = None
        self.stream_thread: Optional[threading.Thread] = None

        # State tracking
        self.active = False
        self.last_activity_time = 0
        self.silence_threshold = 2.0  # seconds of silence before considering inactive

        # Restart backoff mechanism
        self.restart_count = 0
        self.last_restart_time = 0
        self.max_restarts = 10  # Increased maximum number of restarts before cooling down
        self.restart_cooldown = 20.0  # Reduced cooldown time
        self.backoff_factor = 1.5  # Reduced backoff factor for more frequent restarts
        self.min_restart_interval = 2.0  # Reduced minimum seconds between restarts

        # Track consecutive buffer overflows
        self.consecutive_overflows = 0
        self.last_overflow_time = 0
        self.overflow_reset_interval = 30.0  # Reset consecutive count after this many seconds

        # Buffer for storing audio during restarts
        self.pending_audio = []
        self.max_pending_audio = 5  # Reduced to prevent buffer buildup during restart

        # Create communication queues
        self.control_queue = queue.Queue()

    def start(self):
        """Start the transcriber for this user."""
        if self.active:
            return

        self.active = True

        # Create the transcriber using our wrapper methods with optimized settings
        self.transcriber = aai.RealtimeTranscriber(
            sample_rate=self.sample_rate,
            on_data=lambda transcript: self._on_data_wrapper(transcript),
            on_error=lambda error: self._on_error_wrapper(error),
            on_open=lambda session: self._on_open_wrapper(session),
            on_close=lambda: self._on_close_wrapper(),
            # Configure end utterance silence threshold to be shorter
            end_utterance_silence_threshold=800,  # 800ms instead of default 700ms
            # Enable extra session information
            on_extra_session_information=lambda info: print(f"Session info for user {self.user_id}: {info.audio_duration_seconds}s audio processed")
        )

        # Connect to AssemblyAI
        try:
            self.transcriber.connect()
        except Exception as e:
            print(f"Error connecting to AssemblyAI for user {self.user_id}: {e}")
            self.active = False
            return

        # Create an audio stream adapter with smaller chunk size
        self.audio_stream = AudioStreamAdapter(
            sample_rate=self.sample_rate,
            chunk_size=600  # Use smaller chunks by default
        )

        # Start streaming in a non-blocking way
        def stream_audio():
            try:
                print(f"Started streaming audio for user {self.user_id} to AssemblyAI")
                self.transcriber.stream(self.audio_stream)
            except Exception as e:
                print(f"Error in audio streaming for user {self.user_id}: {e}")
                if self.on_transcript:
                    self.on_transcript({
                        'user_id': self.user_id,
                        'username': self.username,
                        'error': f"Audio streaming error: {e}",
                        'timestamp': datetime.datetime.now().isoformat()
                    })

        # Start streaming in a separate thread
        self.stream_thread = threading.Thread(target=stream_audio, daemon=True)
        self.stream_thread.start()

        # Reset activity time
        self.last_activity_time = time.time()

    def stop(self):
        """Stop the transcriber for this user."""
        if not self.active:
            return

        self.active = False

        # Close the audio stream
        if self.audio_stream:
            try:
                self.audio_stream.close()
            except Exception as e:
                print(f"Error closing audio stream for user {self.user_id}: {e}")

        # Close the transcriber
        if self.transcriber:
            try:
                self.transcriber.close()
            except Exception as e:
                print(f"Error closing transcriber for user {self.user_id}: {e}")

        # Wait for the thread to finish (with timeout)
        if self.stream_thread and self.stream_thread.is_alive():
            try:
                self.stream_thread.join(timeout=1.0)
            except Exception as e:
                print(f"Error joining stream thread for user {self.user_id}: {e}")

    def add_audio(self, audio_data: bytes):
        """Add audio data to the stream.

        Args:
            audio_data: Audio bytes to process
        """
        # Always update activity time when receiving audio
        self.last_activity_time = time.time()

        # If not active or no stream, buffer the audio for later
        if not self.active or not self.audio_stream:
            # Store audio in pending buffer if we're in the process of restarting
            if len(self.pending_audio) < self.max_pending_audio:
                self.pending_audio.append(audio_data)
            return

        # Add the audio data to the stream
        self.audio_stream.add_audio(audio_data)

    def is_active(self) -> bool:
        """Check if this user is actively speaking.

        Returns:
            bool: True if the user is active, False otherwise
        """
        if not self.active:
            return False

        # Check if we've received audio recently
        current_time = time.time()
        return (current_time - self.last_activity_time) < self.silence_threshold

    def _restart_transcriber(self):
        """Restart the transcriber after an error.

        This method cleanly stops the current transcriber and starts a new one,
        with exponential backoff to prevent rapid restarts.
        """
        current_time = time.time()

        # Check if we're in a cooldown period
        if self.restart_count >= self.max_restarts:
            cooldown_remaining = self.last_restart_time + self.restart_cooldown - current_time
            if cooldown_remaining > 0:
                print(f"Transcriber for user {self.user_id} in cooldown period, {cooldown_remaining:.1f}s remaining")
                # Send a message to the user
                if self.on_transcript:
                    self.on_transcript({
                        'user_id': self.user_id,
                        'username': self.username,
                        'text': f"[Transcription paused due to errors, will resume in {cooldown_remaining:.0f}s]",
                        'timestamp': datetime.datetime.now().isoformat(),
                        'is_final': True
                    })
                return
            else:
                # Reset restart count after cooldown
                self.restart_count = 0

        # Calculate backoff time
        if self.restart_count > 0:
            backoff_time = self.min_restart_interval * (self.backoff_factor ** (self.restart_count - 1))
            time_since_last_restart = current_time - self.last_restart_time

            if time_since_last_restart < backoff_time:
                print(f"Backing off restart for user {self.user_id}, waiting {backoff_time-time_since_last_restart:.1f}s")
                return

        # Update restart tracking
        self.restart_count += 1
        self.last_restart_time = current_time

        print(f"Restarting transcriber for user {self.user_id} (restart #{self.restart_count})")

        # Temporarily mark as inactive to buffer incoming audio
        self.active = False

        # Stop the current transcriber
        if self.transcriber:
            try:
                self.transcriber.close()
            except Exception as e:
                print(f"Error closing transcriber during restart for user {self.user_id}: {e}")

        # Close the current audio stream
        if self.audio_stream:
            try:
                self.audio_stream.close()
            except Exception as e:
                print(f"Error closing audio stream during restart for user {self.user_id}: {e}")

        # Wait for the thread to finish (with timeout)
        if self.stream_thread and self.stream_thread.is_alive():
            try:
                self.stream_thread.join(timeout=0.5)
            except Exception as e:
                print(f"Error joining stream thread during restart for user {self.user_id}: {e}")

        # Create a new audio stream with progressively smaller chunk size based on restart count
        chunk_size = max(400, 800 - (self.restart_count * 50))  # Decrease chunk size with each restart

        self.audio_stream = AudioStreamAdapter(
            sample_rate=self.sample_rate,
            chunk_size=chunk_size  # Use progressively smaller chunks
        )

        print(f"Using chunk size of {chunk_size} bytes for restart #{self.restart_count}")

        # Create a new transcriber with even more aggressive settings based on restart count
        # Progressively reduce the silence threshold with each restart
        silence_threshold = max(700, 800 - (self.restart_count * 50))

        self.transcriber = aai.RealtimeTranscriber(
            sample_rate=self.sample_rate,
            on_data=lambda transcript: self._on_data_wrapper(transcript),
            on_error=lambda error: self._on_error_wrapper(error),
            on_open=lambda session: self._on_open_wrapper(session),
            on_close=lambda: self._on_close_wrapper(),
            # Configure end utterance silence threshold to be shorter
            end_utterance_silence_threshold=silence_threshold,
            # Enable extra session information
            on_extra_session_information=lambda info: print(f"Session info for user {self.user_id}: {info.audio_duration_seconds}s audio processed")
        )

        print(f"Using silence threshold of {silence_threshold}ms for restart #{self.restart_count}")

        # Connect to AssemblyAI
        try:
            self.transcriber.connect()
        except Exception as e:
            print(f"Error connecting to AssemblyAI during restart for user {self.user_id}: {e}")
            return

        # Start streaming in a non-blocking way
        def stream_audio():
            try:
                print(f"Restarted streaming audio for user {self.user_id} to AssemblyAI")
                self.transcriber.stream(self.audio_stream)
            except Exception as e:
                print(f"Error in restarted audio streaming for user {self.user_id}: {e}")
                if self.on_transcript:
                    self.on_transcript({
                        'user_id': self.user_id,
                        'username': self.username,
                        'error': f"Audio streaming error after restart: {e}",
                        'timestamp': datetime.datetime.now().isoformat()
                    })

        # Start streaming in a separate thread
        self.stream_thread = threading.Thread(target=stream_audio, daemon=True)
        self.stream_thread.start()

        # Mark as active again
        self.active = True

        # Process any pending audio
        pending_audio_copy = list(self.pending_audio)
        self.pending_audio.clear()

        for audio_chunk in pending_audio_copy:
            self.audio_stream.add_audio(audio_chunk)

        # Notify that we've restarted
        restart_message = "[Transcription restarted]"
        if self.restart_count > 1:
            restart_message = f"[Transcription restarted (attempt {self.restart_count})]"

        if self.on_transcript:
            self.on_transcript({
                'user_id': self.user_id,
                'username': self.username,
                'text': restart_message,
                'timestamp': datetime.datetime.now().isoformat(),
                'is_final': True
            })

    # Wrapper methods for callbacks to avoid issues with lambda capturing
    def _on_data_wrapper(self, transcript):
        if not transcript.text:
            return

        # Update last activity time
        self.last_activity_time = time.time()

        # Create timestamp for the transcript
        timestamp = datetime.datetime.now().isoformat()

        # Call the callback with the transcript data
        if self.on_transcript:
            self.on_transcript({
                'user_id': self.user_id,
                'username': self.username,
                'text': transcript.text,
                'timestamp': timestamp,
                'is_final': isinstance(transcript, aai.RealtimeFinalTranscript)
            })

    def _on_error_wrapper(self, error):
        error_str = str(error)
        print(f"AssemblyAI error for user {self.user_id}: {error_str}")

        # Check if this is a buffer overflow error
        if "read limited at" in error_str:
            current_time = time.time()

            # Check if this is a consecutive overflow
            if current_time - self.last_overflow_time < self.overflow_reset_interval:
                self.consecutive_overflows += 1
            else:
                # Reset if it's been a while since the last overflow
                self.consecutive_overflows = 1

            self.last_overflow_time = current_time

            print(f"Buffer overflow detected for user {self.user_id}, attempting recovery (consecutive: {self.consecutive_overflows})")

            # If we've had multiple consecutive overflows, take more drastic action
            if self.consecutive_overflows >= 3:
                # Force a cooldown period
                print(f"Multiple consecutive buffer overflows for user {self.user_id}, forcing cooldown")

                # Close the current session completely
                if self.transcriber:
                    try:
                        self.transcriber.close()
                    except Exception as e:
                        print(f"Error closing transcriber during forced cooldown: {e}")

                # Clear any pending audio
                self.pending_audio.clear()

                # Wait a bit before restarting
                time.sleep(0.5)

            # Try to restart the transcriber
            self._restart_transcriber()
            return

        # For other errors, forward to the main thread
        if self.on_transcript:
            self.on_transcript({
                'user_id': self.user_id,
                'username': self.username,
                'error': error_str,
                'timestamp': datetime.datetime.now().isoformat()
            })

    def _on_open_wrapper(self, session_opened):
        print(f"AssemblyAI session opened for user {self.user_id}: {session_opened.session_id}")

    def _on_close_wrapper(self):
        print(f"AssemblyAI session closed for user {self.user_id}")
        # Don't send a 'closed' signal to avoid terminating the main processing task
        # Instead, just log it and let the transcriber restart if needed


# Import this at the end to avoid circular imports
from src.utils.realtime_sink import AudioStreamAdapter
