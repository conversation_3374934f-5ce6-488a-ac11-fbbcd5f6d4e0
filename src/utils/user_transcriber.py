"""
User transcriber module for real-time speech recognition.

This module provides a class that handles real-time transcription for a user
using the Whisper speech recognition model.
"""

import threading
import queue
import time
import datetime
from typing import Dict, Any, Optional, Callable

# Import our custom Whisper transcriber
from src.utils.whisper_transcriber import WhisperTranscriber
from src.config.config import WHISPER_MODEL_SIZE
from src.utils.realtime_sink import AudioStreamAdapter

class UserTranscriber:
    """Handles real-time transcription for a single user."""

    def __init__(self,
                 user_id: int,
                 username: str,
                 sample_rate: int = 16000,
                 on_transcript: Optional[Callable[[Dict[str, Any]], None]] = None):
        """Initialize the user transcriber.

        Args:
            user_id: User ID
            username: Username for display
            sample_rate: Audio sample rate
            on_transcript: Callback function for new transcripts
        """
        self.user_id = user_id
        self.username = username
        self.sample_rate = sample_rate
        self.on_transcript = on_transcript

        # Create the Whisper transcriber
        self.transcriber = WhisperTranscriber(
            user_id=user_id,
            username=username,
            sample_rate=sample_rate,
            on_transcript=on_transcript,
            model_size=WHISPER_MODEL_SIZE
        )

        # State tracking
        self.active = False
        self.last_activity_time = 0
        self.silence_threshold = 2.0  # seconds of silence before considering inactive
        
        # Restart backoff mechanism
        self.restart_count = 0
        self.last_restart_time = 0
        self.max_restarts = 10  # Maximum number of restarts before cooling down
        self.restart_cooldown = 20.0  # Cooldown time
        self.backoff_factor = 1.5  # Backoff factor for more frequent restarts
        self.min_restart_interval = 2.0  # Minimum seconds between restarts
        
        # Track consecutive buffer overflows
        self.consecutive_overflows = 0
        self.last_overflow_time = 0
        self.overflow_reset_interval = 30.0  # Reset consecutive count after this many seconds
        
        # Buffer for storing audio during restarts
        self.pending_audio = []
        self.max_pending_audio = 5  # Prevent buffer buildup during restart
        
        # Create communication queues
        self.control_queue = queue.Queue()

    def start(self):
        """Start the transcriber for this user."""
        if self.active:
            return

        # Start the transcriber
        self.transcriber.start()
        
        # Mark as active
        self.active = True
        
        # Reset activity time
        self.last_activity_time = time.time()

    def stop(self):
        """Stop the transcriber for this user."""
        if not self.active:
            return

        self.active = False
        
        # Stop the transcriber
        self.transcriber.stop()

    def add_audio(self, audio_data: bytes):
        """Add audio data to the stream.

        Args:
            audio_data: Audio bytes to process
        """
        # Always update activity time when receiving audio
        self.last_activity_time = time.time()

        # If not active, buffer the audio for later
        if not self.active:
            # Store audio in pending buffer if we're in the process of restarting
            if len(self.pending_audio) < self.max_pending_audio:
                self.pending_audio.append(audio_data)
            return

        # Add the audio data to the transcriber
        self.transcriber.add_audio(audio_data)

    def is_active(self) -> bool:
        """Check if this user is actively speaking.

        Returns:
            bool: True if the user is active, False otherwise
        """
        if not self.active:
            return False

        # Check if we've received audio recently
        current_time = time.time()
        return (current_time - self.last_activity_time) < self.silence_threshold

    def _restart_transcriber(self):
        """Restart the transcriber after an error.

        This method cleanly stops the current transcriber and starts a new one,
        with exponential backoff to prevent rapid restarts.
        """
        current_time = time.time()

        # Check if we're in a cooldown period
        if self.restart_count >= self.max_restarts:
            cooldown_remaining = self.last_restart_time + self.restart_cooldown - current_time
            if cooldown_remaining > 0:
                print(f"Transcriber for user {self.user_id} in cooldown period, {cooldown_remaining:.1f}s remaining")
                # Send a message to the user
                if self.on_transcript:
                    self.on_transcript({
                        'user_id': self.user_id,
                        'username': self.username,
                        'text': f"[Transcription paused due to errors, will resume in {cooldown_remaining:.0f}s]",
                        'timestamp': datetime.datetime.now().isoformat(),
                        'is_final': True
                    })
                return
            else:
                # Reset restart count after cooldown
                self.restart_count = 0

        # Calculate backoff time
        if self.restart_count > 0:
            backoff_time = self.min_restart_interval * (self.backoff_factor ** (self.restart_count - 1))
            time_since_last_restart = current_time - self.last_restart_time

            if time_since_last_restart < backoff_time:
                print(f"Backing off restart for user {self.user_id}, waiting {backoff_time-time_since_last_restart:.1f}s")
                return

        # Update restart tracking
        self.restart_count += 1
        self.last_restart_time = current_time

        print(f"Restarting transcriber for user {self.user_id} (restart #{self.restart_count})")

        # Temporarily mark as inactive to buffer incoming audio
        self.active = False
        
        # Stop the current transcriber
        self.transcriber.stop()
        
        # Create a new transcriber
        self.transcriber = WhisperTranscriber(
            user_id=self.user_id,
            username=self.username,
            sample_rate=self.sample_rate,
            on_transcript=self.on_transcript,
            model_size=WHISPER_MODEL_SIZE
        )
        
        # Start the new transcriber
        self.transcriber.start()
        
        # Mark as active again
        self.active = True
        
        # Process any pending audio
        pending_audio_copy = list(self.pending_audio)
        self.pending_audio.clear()
        
        for audio_chunk in pending_audio_copy:
            self.transcriber.add_audio(audio_chunk)
        
        # Notify that we've restarted
        restart_message = "[Transcription restarted]"
        if self.restart_count > 1:
            restart_message = f"[Transcription restarted (attempt {self.restart_count})]"
        
        if self.on_transcript:
            self.on_transcript({
                'user_id': self.user_id,
                'username': self.username,
                'text': restart_message,
                'timestamp': datetime.datetime.now().isoformat(),
                'is_final': True
            })
