"""
Function calling framework for Sidenvoice.

This module provides a framework for defining and executing functions that can be
called by the Gemini AI model.
"""

import asyncio
import json
import logging
from typing import Dict, List, Any, Optional, Union
from google.genai import types

# Set up logging
logger = logging.getLogger('sidenvoice.function_calling')
if not logger.handlers:
    handler = logging.StreamHandler()
    formatter = logging.Formatter('%(asctime)s - %(name)s - %(levelname)s - %(message)s')
    handler.setFormatter(formatter)
    logger.addHandler(handler)
    logger.setLevel(logging.INFO)

class FunctionRegistry:
    """Registry for function handlers."""
    _instance = None
    _handlers = {}

    def __new__(cls):
        if cls._instance is None:
            cls._instance = super().__new__(cls)
        return cls._instance

    @classmethod
    def register(cls, name: str, description: str, parameters: Dict[str, Any]):
        """Register a function handler.
        
        Args:
            name: The name of the function
            description: Description of what the function does
            parameters: JSON schema for the function parameters
        """
        def decorator(func):
            cls._handlers[name] = {
                'function': func,
                'description': description,
                'parameters': parameters
            }
            return func
        return decorator

    @classmethod
    async def call_function(cls, name: str, **kwargs) -> Any:
        """Call a registered function by name.
        
        Args:
            name: Name of the function to call
            **kwargs: Arguments to pass to the function
            
        Returns:
            Result of the function call
        """
        if name not in cls._handlers:
            raise ValueError(f"No function registered with name: {name}")
            
        handler = cls._handlers[name]
        return await handler['function'](**kwargs)

    @classmethod
    def get_function_declarations(cls) -> List[Dict[str, Any]]:
        """Get function declarations for all registered functions."""
        return [
            {
                'name': name,
                'description': info['description'],
                'parameters': info['parameters']
            }
            for name, info in cls._handlers.items()
        ]

async def process_function_call(function_call: types.FunctionCall, user_id: str = "0") -> Dict[str, Any]:
    """Process a function call from the AI.

    Args:
        function_call: The function call from the AI
        user_id: The ID of the user making the request (optional)

    Returns:
        The result of the function call
    """
    function_name = function_call.name
    function_args = {}
    
    # Parse function arguments
    if function_call.args:
        try:
            if isinstance(function_call.args, str):
                # Handle string arguments (older API versions)
                function_args = json.loads(function_call.args)
            else:
                # Handle dict arguments (newer API versions)
                function_args = function_call.args
        except json.JSONDecodeError as e:
            logger.error(f"Error parsing function arguments: {e}")
            return {"error": f"Error parsing function arguments: {e}"}
    
    # Add user_id to function arguments
    function_args["user_id"] = user_id
    
    # Get the function handler
    function_handler = FunctionRegistry.get_function(function_name)
    
    if not function_handler:
        logger.error(f"Function '{function_name}' not found")
        return {"error": f"Function '{function_name}' not found"}
    
    # Execute the function
    try:
        logger.info(f"Executing function '{function_name}' with arguments: {function_args}")
        result = await function_handler.execute(**function_args)
        logger.info(f"Function '{function_name}' executed successfully")
        return result
    except Exception as e:
        import traceback
        logger.error(f"Error executing function '{function_name}': {e}")
        logger.error(traceback.format_exc())
        return {"error": f"Error executing function '{function_name}': {e}"}

def get_function_declarations() -> List[Dict[str, Any]]:
    """Get function declarations for all registered functions.

    Returns:
        A list of function declarations
    """
    return FunctionRegistry.get_function_declarations()
