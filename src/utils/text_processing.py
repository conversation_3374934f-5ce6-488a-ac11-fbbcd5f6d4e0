"""
Text processing utilities for VCAgentBot.

This module provides functions for processing and sanitizing text,
particularly for preparing AI-generated text for text-to-speech.
"""

import re
from typing import Optional, List

def sanitize_markdown(text: str) -> str:
    """Remove markdown formatting elements from text.

    This function removes common markdown formatting elements that would
    cause unnatural speech when read by a text-to-speech engine.

    Args:
        text: The text to sanitize

    Returns:
        str: The sanitized text with markdown elements removed
    """
    if not text:
        return ""

    # Store the original text for comparison
    original_text = text

    # Remove code blocks (```code```)
    text = re.sub(r'```[\s\S]*?```', '', text)

    # Remove inline code (`code`)
    text = re.sub(r'`([^`]+)`', r'\1', text)

    # Remove bold/italic formatting
    text = re.sub(r'\*\*\*([^*]+)\*\*\*', r'\1', text)  # ***bold italic***
    text = re.sub(r'\*\*([^*]+)\*\*', r'\1', text)      # **bold**
    text = re.sub(r'\*([^*]+)\*', r'\1', text)          # *italic*
    text = re.sub(r'___([^_]+)___', r'\1', text)        # ___bold italic___
    text = re.sub(r'__([^_]+)__', r'\1', text)          # __bold__
    text = re.sub(r'_([^_]+)_', r'\1', text)            # _italic_

    # Remove strikethrough
    text = re.sub(r'~~([^~]+)~~', r'\1', text)          # ~~strikethrough~~

    # Remove headers
    text = re.sub(r'^#{1,6}\s+(.+)$', r'\1', text, flags=re.MULTILINE)

    # Remove blockquotes
    text = re.sub(r'^>\s+(.+)$', r'\1', text, flags=re.MULTILINE)
    text = re.sub(r'^>\s*$', '', text, flags=re.MULTILINE)

    # Remove horizontal rules
    text = re.sub(r'^-{3,}$|^_{3,}$|^\*{3,}$', '', text, flags=re.MULTILINE)

    # Remove bullet points and numbered lists
    text = re.sub(r'^\s*[-*+]\s+(.+)$', r'\1', text, flags=re.MULTILINE)  # Bullet points
    text = re.sub(r'^\s*\d+\.\s+(.+)$', r'\1', text, flags=re.MULTILINE)  # Numbered lists

    # Remove link formatting but keep the text
    text = re.sub(r'\[([^\]]+)\]\([^\)]+\)', r'\1', text)  # [text](url)

    # Remove HTML tags
    text = re.sub(r'<[^>]+>', '', text)

    # Remove excessive whitespace
    text = re.sub(r'\n{3,}', '\n\n', text)  # Replace 3+ newlines with 2
    text = re.sub(r'\s{2,}', ' ', text)     # Replace 2+ spaces with 1
    text = text.strip()                     # Remove leading/trailing whitespace

    # If we've removed everything, return a message
    if not text and original_text:
        return "I have a response for you, but it contains only formatting elements."

    return text

def chunk_text_for_tts(text: str, max_length: int = 500) -> list:
    """Split text into chunks suitable for TTS processing.

    Args:
        text: The text to split
        max_length: Maximum length of each chunk

    Returns:
        list: List of text chunks
    """
    if not text:
        return []

    if len(text) <= max_length:
        return [text]

    # Try to find a good breakpoint (end of sentence)
    breakpoint = text.rfind('.', 0, max_length)
    if breakpoint == -1:  # No period found
        breakpoint = text.rfind('!', 0, max_length)
    if breakpoint == -1:  # No exclamation found
        breakpoint = text.rfind('?', 0, max_length)
    if breakpoint == -1:  # No question mark found
        breakpoint = text.rfind(' ', 0, max_length)

    if breakpoint == -1:  # No good breakpoint found
        # Just split at max_length
        first_chunk = text[:max_length]
        remaining = text[max_length:]
    else:
        # Split at the breakpoint
        first_chunk = text[:breakpoint+1]
        remaining = text[breakpoint+1:]

    # Recursively chunk the remaining text
    return [first_chunk] + chunk_text_for_tts(remaining, max_length)
