"""
Text-to-speech integration for Sidenvoice using ElevenLabs.

This module handles:
1. Converting text to speech using ElevenLabs API
2. Caching speech audio to reduce API calls
3. Saving speech audio to files for web playback
"""

import os
import time
import hashlib
import asyncio
import aiohttp
import json
from pathlib import Path
from typing import Optional, Dict, Any, List, Tuple

from src.config.ai_config import (
    ELEVENLABS_API_KEY,
    ELEVENLABS_ENABLED,
    ELEVENLABS_VOICE_ID,
    ELEVENLABS_MODEL_ID,
    ELEVENLABS_STABILITY,
    ELEVENLABS_SIMILARITY_BOOST,
    TTS_CACHE_ENABLED,
    TTS_CACHE_DIR,
    TTS_CACHE_MAX_SIZE_MB
)
from src.utils.text_processing import sanitize_markdown, chunk_text_for_tts

class ElevenLabsTTS:
    """Handles text-to-speech using ElevenLabs API for web applications."""

    def __init__(self, api_key: str = ELEVENLABS_API_KEY):
        """Initialize the TTS integration.

        Args:
            api_key: ElevenLabs API key
        """
        self.api_key = api_key
        self.voice_id = ELEVENLABS_VOICE_ID
        self.model_id = ELEVENLABS_MODEL_ID
        self.stability = ELEVENLABS_STABILITY
        self.similarity_boost = ELEVENLABS_SIMILARITY_BOOST

        # API endpoints
        self.base_url = "https://api.elevenlabs.io/v1"
        self.tts_endpoint = f"{self.base_url}/text-to-speech"

        # Cache settings
        self.cache_enabled = TTS_CACHE_ENABLED
        self.cache_dir = TTS_CACHE_DIR
        self.cache_max_size_mb = TTS_CACHE_MAX_SIZE_MB

        # Rate limiting
        self.last_request_time = 0
        self.min_request_interval = 1.0  # seconds

        # Initialize cache directory if enabled
        if self.cache_enabled:
            os.makedirs(self.cache_dir, exist_ok=True)
            print(f"TTS cache directory initialized at {self.cache_dir}")

        # Set enabled status
        self.enabled = ELEVENLABS_ENABLED

        # Check if we have an API key for generating new TTS
        self.can_generate = bool(self.api_key)

        if self.enabled:
            if self.can_generate:
                print("ElevenLabs TTS initialized with full functionality")
            else:
                print("ElevenLabs TTS initialized in playback-only mode (no API key for new generation)")
        else:
            print("ElevenLabs TTS integration is disabled")

    def set_inactivity_monitor(self, inactivity_monitor):
        """Set the inactivity monitor.

        Args:
            inactivity_monitor: The inactivity monitor instance
        """
        self.inactivity_monitor = inactivity_monitor

    async def generate_speech(self,
                             text: str,
                             voice_id: str = None) -> Optional[bytes]:
        """Generate speech from text.

        Args:
            text: Text to convert to speech
            voice_id: Voice ID to use (defaults to configured voice)

        Returns:
            bytes: Audio data or None if generation failed
        """
        if not self.enabled:
            print("TTS is disabled, cannot generate speech")
            return None

        if not text:
            print("Cannot generate speech from empty text")
            return None

        # Use default voice if not specified
        voice_id = voice_id or self.voice_id

        # Check cache first if enabled
        if self.cache_enabled:
            cache_key = self._generate_cache_key(text, voice_id)
            cached_audio = self._get_from_cache(cache_key)

            if cached_audio:
                print(f"Using cached TTS audio for: {text[:30]}...")
                return cached_audio

        # If we don't have an API key, we can't generate new speech
        if not self.can_generate:
            print("No API key available, cannot generate new speech")
            return None

        # Prepare request
        url = f"{self.tts_endpoint}/{voice_id}"

        headers = {
            "Accept": "audio/mpeg",
            "Content-Type": "application/json",
            "xi-api-key": self.api_key
        }

        payload = {
            "text": text,
            "model_id": self.model_id,
            "voice_settings": {
                "stability": self.stability,
                "similarity_boost": self.similarity_boost,
                "speed": 1.2
            }
        }

        try:
            print(f"Generating new TTS audio for: {text[:30]}...")
            async with aiohttp.ClientSession() as session:
                async with session.post(url, json=payload, headers=headers) as response:
                    if response.status == 200:
                        audio_data = await response.read()

                        # Cache the result if enabled
                        if self.cache_enabled:
                            self._save_to_cache(cache_key, audio_data)
                            print(f"Saved TTS audio to cache with key: {cache_key}")

                        return audio_data
                    else:
                        error_text = await response.text()
                        print(f"ElevenLabs API error ({response.status}): {error_text}")
                        return None

        except Exception as e:
            print(f"Error generating speech: {e}")
            return None

    async def play_speech(self,
                         voice_client: discord.VoiceClient,
                         text: str,
                         voice_id: str = None) -> bool:
        """Generate speech and play it in a Discord voice channel.

        Args:
            voice_client: Discord voice client
            text: Text to convert to speech
            voice_id: Voice ID to use

        Returns:
            bool: True if played successfully, False otherwise
        """
        if not voice_client or not voice_client.is_connected():
            print("Cannot play speech: Voice client not connected")
            return False

        # Sanitize the text to remove any markdown formatting
        text = sanitize_markdown(text)

        # Check if text is too long - ElevenLabs free tier has limitations
        # Split into smaller chunks for better reliability
        if len(text) > 150:
            return await self._play_chunked_speech(voice_client, text, voice_id)

        # Generate speech for a single chunk
        audio_data = await self.generate_speech(text, voice_id)

        if not audio_data:
            print("Failed to generate speech audio")
            return False

        # Get the cache key for this text/voice combination
        cache_key = self._generate_cache_key(text, voice_id or self.voice_id)
        cache_path = os.path.join(self.cache_dir, f"{cache_key}.mp3")

        try:
            # Check if already playing
            if voice_client.is_playing():
                voice_client.stop()
                # Add a small delay to ensure the previous playback has fully stopped
                await asyncio.sleep(0.5)

            # First try to play directly from the cached file if it exists
            if os.path.exists(cache_path):
                print(f"Playing TTS from cache file: {cache_path}")
                try:
                    # Create audio source from file
                    audio_source = discord.FFmpegPCMAudio(
                        cache_path,
                        options='-loglevel warning'
                    )

                    # Play the audio
                    voice_client.play(audio_source)

                    # Update inactivity monitor if available
                    if self.inactivity_monitor and voice_client.guild:
                        self.inactivity_monitor.update_activity(voice_client.guild.id)

                    print(f"Playing TTS audio for: {text[:30]}...")
                    return True
                except Exception as file_e:
                    print(f"Error playing from cache file, falling back to memory stream: {file_e}")
                    # Fall through to memory stream method

            # If we get here, either the file doesn't exist or playing from file failed
            # Create a temporary file to ensure the audio data is properly handled
            temp_file_path = os.path.join(self.cache_dir, f"temp_{int(time.time())}.mp3")
            with open(temp_file_path, 'wb') as f:
                f.write(audio_data)

            # Create audio source from the temp file
            audio_source = discord.FFmpegPCMAudio(
                temp_file_path,
                options='-loglevel warning'
            )

            # Play the audio
            voice_client.play(audio_source, after=lambda e: self._cleanup_temp_file(temp_file_path, e))

            # Update inactivity monitor if available
            if self.inactivity_monitor and voice_client.guild:
                self.inactivity_monitor.update_activity(voice_client.guild.id)

            print(f"Playing TTS audio for: {text[:30]}... from temp file")
            return True

        except Exception as e:
            print(f"Error playing TTS audio: {e}")
            # Try one more fallback method with different FFmpeg options
            try:
                print("Attempting fallback playback method...")
                # Save to a temporary file with a unique name
                temp_file_path = os.path.join(self.cache_dir, f"fallback_{int(time.time())}.mp3")
                with open(temp_file_path, 'wb') as f:
                    f.write(audio_data)

                # Use different FFmpeg options
                audio_source = discord.FFmpegPCMAudio(
                    temp_file_path,
                    options='-loglevel warning -af "volume=2.0"'  # Increase volume and add warning level
                )

                # Play with the fallback source
                voice_client.play(audio_source, after=lambda e: self._cleanup_temp_file(temp_file_path, e))

                # Update inactivity monitor if available
                if self.inactivity_monitor and voice_client.guild:
                    self.inactivity_monitor.update_activity(voice_client.guild.id)

                print(f"Playing TTS audio with fallback method for: {text[:30]}...")
                return True
            except Exception as fallback_e:
                print(f"Fallback playback method also failed: {fallback_e}")
                return False

    async def generate_full_response_audio(self,
                                    text: str,
                                    voice_id: str = None) -> Optional[str]:
        """Generate a single audio file for a complete response.

        This method generates a single audio file for the entire response
        instead of chunking it and playing piece by piece.

        Args:
            text: Text to convert to speech
            voice_id: Voice ID to use

        Returns:
            str: Path to the generated audio file or None if generation failed
        """
        if not self.enabled:
            print("TTS is disabled, cannot generate full response audio")
            return None

        if not text:
            print("Cannot generate speech from empty text")
            return None

        # Sanitize the text to remove any markdown formatting
        text = sanitize_markdown(text)

        # Note: The 800 character limit is now applied in the play_full_response and
        # _play_chunked_speech methods before calling this method, so we don't need
        # to apply it again here. This ensures we don't waste API calls on text
        # that won't be converted to speech.

        # Use default voice if not specified
        voice_id = voice_id or self.voice_id

        # Generate a unique cache key for the full response
        cache_key = self._generate_cache_key(text, voice_id)
        cache_path = os.path.join(self.cache_dir, f"full_{cache_key}.mp3")

        # Check if we already have this response cached
        if self.cache_enabled and os.path.exists(cache_path):
            print(f"Using cached full response audio: {cache_path}")
            return cache_path

        # ElevenLabs API has a limit on text length (typically around 5000 chars)
        # We'll use a more conservative limit to ensure reliability
        elevenlabs_max_length = 1000  # Conservative limit for ElevenLabs API

        if len(text) > elevenlabs_max_length:
            print(f"Text is too long ({len(text)} chars), splitting into chunks for generation")
            try:
                # Try to generate combined audio file
                result = await self._generate_combined_audio_file(text, voice_id, cache_path)
                if result:
                    return result
                else:
                    print("Failed to generate combined audio file, will try alternative approach")
                    # If combined approach fails, we'll fall through to the next approach
            except Exception as e:
                print(f"Error in combined audio generation: {e}")
                # Continue with alternative approach

            # If we get here, the combined approach failed
            print("Attempting alternative chunking approach...")

            # Split text into smaller chunks that can be processed individually
            sentences = self._split_into_sentences(text)
            chunks = []
            current_chunk = ""

            for sentence in sentences:
                if len(current_chunk) + len(sentence) <= elevenlabs_max_length:
                    current_chunk += sentence
                else:
                    if current_chunk:
                        chunks.append(current_chunk)
                    current_chunk = sentence

            # Add the last chunk if it's not empty
            if current_chunk:
                chunks.append(current_chunk)

            print(f"Split text into {len(chunks)} chunks for alternative generation approach")

            # Generate audio for the first chunk only as a fallback
            if chunks:
                first_chunk = chunks[0]
                print(f"Generating audio for first chunk only ({len(first_chunk)} chars)")

                # Generate a unique cache key for this chunk
                chunk_cache_key = self._generate_cache_key(first_chunk, voice_id)
                chunk_cache_path = os.path.join(self.cache_dir, f"chunk_{chunk_cache_key}.mp3")

                # Generate speech for this chunk
                audio_data = await self.generate_speech(first_chunk, voice_id)

                if audio_data:
                    # Save the audio data to a file
                    try:
                        with open(chunk_cache_path, 'wb') as f:
                            f.write(audio_data)
                        print(f"Saved first chunk audio to: {chunk_cache_path}")
                        return chunk_cache_path
                    except Exception as e:
                        print(f"Error saving first chunk audio: {e}")

                print("Failed to generate audio for first chunk")
                return None

            print("No chunks available for generation")
            return None

        # For shorter text, generate in a single API call
        print(f"Generating audio for text in a single API call ({len(text)} chars)")
        audio_data = await self.generate_speech(text, voice_id)

        if not audio_data:
            print("Failed to generate full response audio")
            return None

        # Save the audio data to a file
        try:
            with open(cache_path, 'wb') as f:
                f.write(audio_data)
            print(f"Saved full response audio to: {cache_path}")
            return cache_path
        except Exception as e:
            print(f"Error saving full response audio: {e}")
            return None

    async def _generate_combined_audio_file(self,
                                          text: str,
                                          voice_id: str,
                                          output_path: str) -> Optional[str]:
        """Generate a combined audio file from multiple chunks.

        Args:
            text: Text to convert to speech
            voice_id: Voice ID to use
            output_path: Path to save the combined audio file

        Returns:
            str: Path to the combined audio file or None if generation failed
        """
        # Create a unique session ID for this generation to avoid file conflicts
        session_id = int(time.time())

        # Split text into sentences
        sentences = self._split_into_sentences(text)

        # Group sentences into chunks of approximately 500 characters
        chunks = []
        current_chunk = ""

        for sentence in sentences:
            if len(current_chunk) + len(sentence) <= 500:
                current_chunk += sentence
            else:
                if current_chunk:
                    chunks.append(current_chunk)
                current_chunk = sentence

        # Add the last chunk if it's not empty
        if current_chunk:
            chunks.append(current_chunk)

        print(f"Split text into {len(chunks)} chunks for full response audio generation")

        # Generate audio for each chunk and save to temporary files
        temp_files = []

        try:
            for i, chunk in enumerate(chunks):
                print(f"Generating audio for chunk {i+1}/{len(chunks)}")

                # Generate speech for this chunk
                audio_data = await self.generate_speech(chunk, voice_id)

                if not audio_data:
                    print(f"Failed to generate speech for chunk {i+1}")
                    raise Exception(f"Failed to generate speech for chunk {i+1}")

                # Save to a temporary file with consistent naming pattern
                temp_file_path = os.path.join(self.cache_dir, f"chunk_{session_id}_{i}.mp3")
                with open(temp_file_path, 'wb') as f:
                    f.write(audio_data)

                temp_files.append(temp_file_path)
                print(f"Saved chunk {i+1} to {temp_file_path}")

            # Now combine all the temporary files into one
            # Create a list file for ffmpeg with proper path formatting
            list_file_path = os.path.join(self.cache_dir, f"list_{session_id}.txt")

            with open(list_file_path, 'w') as f:
                for temp_file in temp_files:
                    # Convert backslashes to forward slashes for ffmpeg
                    formatted_path = temp_file.replace('\\', '/')
                    f.write(f"file '{formatted_path}'\n")

            print(f"Created ffmpeg list file at {list_file_path}")

            # Use ffmpeg to concatenate the files
            result = subprocess.run([
                'ffmpeg',
                '-f', 'concat',
                '-safe', '0',
                '-i', list_file_path,
                '-c', 'copy',
                output_path
            ], capture_output=True, text=True)

            if result.returncode != 0:
                print(f"Error combining audio files: {result.stderr}")
                # Print the content of the list file for debugging
                with open(list_file_path, 'r') as f:
                    print(f"List file content:\n{f.read()}")
                raise Exception(f"FFmpeg error: {result.stderr}")

            print(f"Successfully combined {len(temp_files)} audio chunks into: {output_path}")
            return output_path

        except Exception as e:
            print(f"Error in audio file generation/combination: {e}")
            return None

        finally:
            # Clean up temporary files in the finally block to ensure they're removed
            for temp_file in temp_files:
                try:
                    if os.path.exists(temp_file):
                        os.remove(temp_file)
                        print(f"Cleaned up temp file: {temp_file}")
                except Exception as cleanup_e:
                    print(f"Error cleaning up temp file {temp_file}: {cleanup_e}")

            # Clean up the list file
            try:
                if 'list_file_path' in locals() and os.path.exists(list_file_path):
                    os.remove(list_file_path)
                    print(f"Cleaned up list file: {list_file_path}")
            except Exception as cleanup_e:
                print(f"Error cleaning up list file: {cleanup_e}")

    async def play_full_response(self,
                               voice_client: discord.VoiceClient,
                               text: str,
                               voice_id: str = None) -> bool:
        """Generate and play a full response audio file.

        Args:
            voice_client: Discord voice client
            text: Text to convert to speech
            voice_id: Voice ID to use

        Returns:
            bool: True if played successfully, False otherwise
        """
        if not voice_client or not voice_client.is_connected():
            print("Cannot play speech: Voice client not connected")
            return False

        print(f"Generating full response audio for text ({len(text)} chars)")

        # Apply the 800 character limit for TTS
        # This happens before any API calls to avoid wasting API usage
        original_length = len(text)
        text = self.truncate_text_for_tts(text, 800)

        if len(text) < original_length:
            print(f"Applied 800 character limit for TTS. Original: {original_length} chars, Truncated: {len(text)} chars")

        # Generate the full response audio file
        audio_file_path = await self.generate_full_response_audio(text, voice_id)

        if not audio_file_path:
            print("Failed to generate full response audio, falling back to chunked playback")
            # Fall back to chunked playback
            return await self._play_chunked_speech(voice_client, text, voice_id)

        try:
            # Check if already playing
            if voice_client.is_playing():
                voice_client.stop()
                # Add a small delay to ensure the previous playback has fully stopped
                await asyncio.sleep(0.5)

            # Verify the file exists before attempting to play
            if not os.path.exists(audio_file_path):
                print(f"Audio file not found at {audio_file_path}, falling back to chunked playback")
                return await self._play_chunked_speech(voice_client, text, voice_id)

            # Create audio source from the file
            audio_source = discord.FFmpegPCMAudio(
                audio_file_path,
                options='-loglevel warning'
            )

            # Play the audio
            voice_client.play(audio_source)

            # Update inactivity monitor if available
            if self.inactivity_monitor and voice_client.guild:
                self.inactivity_monitor.update_activity(voice_client.guild.id)

            print(f"Playing full response audio from: {audio_file_path}")
            return True

        except Exception as e:
            print(f"Error playing full response audio: {e}")
            print("Falling back to chunked playback")
            # Fall back to chunked playback if full response playback fails
            return await self._play_chunked_speech(voice_client, text, voice_id)

    async def _play_chunked_speech(self,
                                  voice_client: discord.VoiceClient,
                                  text: str,
                                  voice_id: str = None) -> bool:
        """Play longer text by splitting it into smaller chunks.

        Args:
            voice_client: Discord voice client
            text: Text to convert to speech
            voice_id: Voice ID to use

        Returns:
            bool: True if played successfully, False otherwise
        """
        # First, sanitize the text to remove any markdown formatting
        text = sanitize_markdown(text)

        # Apply the 800 character limit for TTS
        # This happens before any API calls to avoid wasting API usage
        original_length = len(text)
        text = self.truncate_text_for_tts(text, 800)

        if len(text) < original_length:
            print(f"Applied 800 character limit for chunked TTS. Original: {original_length} chars, Truncated: {len(text)} chars")

        # Split text into sentences
        sentences = self._split_into_sentences(text)

        # Group sentences into chunks of approximately 100-150 characters
        chunks = []
        current_chunk = ""

        for sentence in sentences:
            if len(current_chunk) + len(sentence) <= 150:
                current_chunk += sentence
            else:
                if current_chunk:
                    chunks.append(current_chunk)
                current_chunk = sentence

        # Add the last chunk if it's not empty
        if current_chunk:
            chunks.append(current_chunk)

        print(f"Split text into {len(chunks)} chunks for TTS")

        # Play each chunk in sequence
        for i, chunk in enumerate(chunks):
            print(f"Playing chunk {i+1}/{len(chunks)}: {chunk[:30]}...")

            # Generate speech for this chunk
            audio_data = await self.generate_speech(chunk, voice_id)

            if not audio_data:
                print(f"Failed to generate speech for chunk {i+1}")
                continue

            # Generate speech for this chunk without caching
            # We don't need to use the cache key here since we're creating a temp file

            try:
                # Check if already playing
                if voice_client.is_playing():
                    voice_client.stop()
                    # Add a small delay to ensure the previous playback has fully stopped
                    await asyncio.sleep(0.5)

                # Create a temporary file for this chunk
                temp_file_path = os.path.join(self.cache_dir, f"chunk_{i}_{int(time.time())}.mp3")
                with open(temp_file_path, 'wb') as f:
                    f.write(audio_data)

                # Create audio source from the temp file
                audio_source = discord.FFmpegPCMAudio(
                    temp_file_path,
                    options='-loglevel warning'
                )

                # Play the audio and wait for it to finish
                voice_client.play(audio_source, after=lambda e: self._cleanup_temp_file(temp_file_path, e))

                # Update inactivity monitor if available
                if self.inactivity_monitor and voice_client.guild:
                    self.inactivity_monitor.update_activity(voice_client.guild.id)

                # Wait for the audio to finish playing
                while voice_client.is_playing():
                    await asyncio.sleep(0.1)

                # Add a small delay between chunks
                await asyncio.sleep(0.3)

            except Exception as e:
                print(f"Error playing chunk {i+1}: {e}")

        return True

    def _split_into_sentences(self, text: str) -> list:
        """Split text into sentences.

        Args:
            text: Text to split

        Returns:
            list: List of sentences
        """
        # Simple sentence splitting by punctuation
        # This is a basic implementation and could be improved
        sentences = []
        current_sentence = ""

        for char in text:
            current_sentence += char

            if char in ['.', '!', '?'] and len(current_sentence.strip()) > 0:
                sentences.append(current_sentence)
                current_sentence = ""

        # Add the last sentence if it's not empty
        if current_sentence.strip():
            sentences.append(current_sentence)

        return sentences

    def truncate_text_for_tts(self, text: str, max_chars: int = 800) -> str:
        """Truncate text to a maximum length for TTS, ending at a natural sentence break.

        Args:
            text: Text to truncate
            max_chars: Maximum number of characters

        Returns:
            str: Truncated text with appended message if truncated
        """
        if len(text) <= max_chars:
            return text

        # Use chunk_text_for_tts to find a good breakpoint
        chunks = chunk_text_for_tts(text, max_chars)
        truncated_text = chunks[0]

        # Add standard message about truncation
        truncation_message = " The response is too long to read completely. Please check the text channel for the full message."

        # Make sure we have room for the truncation message
        if len(truncated_text) + len(truncation_message) > max_chars:
            # Find a good breakpoint to ensure we have room for the message
            safe_length = max_chars - len(truncation_message) - 10  # Extra buffer

            # Try to find a sentence end
            breakpoint = truncated_text.rfind('.', 0, safe_length)
            if breakpoint == -1:  # No period found
                breakpoint = truncated_text.rfind('!', 0, safe_length)
            if breakpoint == -1:  # No exclamation found
                breakpoint = truncated_text.rfind('?', 0, safe_length)
            if breakpoint == -1:  # No question mark found
                breakpoint = truncated_text.rfind(' ', 0, safe_length)

            if breakpoint != -1:
                truncated_text = truncated_text[:breakpoint+1]

        # Append the truncation message
        truncated_text += truncation_message

        print(f"Truncated text from {len(text)} to {len(truncated_text)} characters for TTS")
        return truncated_text

    def _cleanup_temp_file(self, file_path: str, error=None):
        """Clean up temporary audio files after playback.

        Args:
            file_path: Path to the temporary file
            error: Any error that occurred during playback
        """
        if error:
            print(f"Error during audio playback: {error}")

        try:
            if os.path.exists(file_path):
                os.remove(file_path)
                print(f"Removed temporary audio file: {file_path}")
        except Exception as e:
            print(f"Error removing temporary audio file: {e}")

    def _generate_cache_key(self, text: str, voice_id: str) -> str:
        """Generate a cache key for the text and voice.

        Args:
            text: Text to convert to speech
            voice_id: Voice ID to use

        Returns:
            str: Cache key
        """
        # Create a hash of the text and voice ID
        hash_input = f"{text}_{voice_id}_{self.model_id}_{self.stability}_{self.similarity_boost}"
        return hashlib.md5(hash_input.encode()).hexdigest()

    def _get_from_cache(self, cache_key: str) -> Optional[bytes]:
        """Get audio data from cache.

        Args:
            cache_key: Cache key

        Returns:
            bytes: Audio data or None if not in cache
        """
        cache_path = os.path.join(self.cache_dir, f"{cache_key}.mp3")

        if os.path.exists(cache_path):
            try:
                with open(cache_path, 'rb') as f:
                    return f.read()
            except Exception as e:
                print(f"Error reading from cache: {e}")

        return None

    def _save_to_cache(self, cache_key: str, audio_data: bytes) -> bool:
        """Save audio data to cache.

        Args:
            cache_key: Cache key
            audio_data: Audio data to cache

        Returns:
            bool: True if saved successfully, False otherwise
        """
        try:
            # Check cache size and clean if needed
            self._clean_cache_if_needed()

            # Save to cache
            cache_path = os.path.join(self.cache_dir, f"{cache_key}.mp3")

            with open(cache_path, 'wb') as f:
                f.write(audio_data)

            return True

        except Exception as e:
            print(f"Error saving to cache: {e}")
            return False

    def _clean_cache_if_needed(self) -> None:
        """Clean the cache if it exceeds the maximum size."""
        if not os.path.exists(self.cache_dir):
            return

        # Get all cache files
        cache_files = [
            os.path.join(self.cache_dir, f)
            for f in os.listdir(self.cache_dir)
            if f.endswith('.mp3')
        ]

        # Calculate total size
        total_size_bytes = sum(os.path.getsize(f) for f in cache_files)
        total_size_mb = total_size_bytes / (1024 * 1024)

        # If cache is too large, remove oldest files
        if total_size_mb > self.cache_max_size_mb:
            # Sort by modification time (oldest first)
            cache_files.sort(key=os.path.getmtime)

            # Remove files until under limit
            for file_path in cache_files:
                if total_size_mb <= self.cache_max_size_mb:
                    break

                file_size_mb = os.path.getsize(file_path) / (1024 * 1024)

                try:
                    os.remove(file_path)
                    total_size_mb -= file_size_mb
                    print(f"Removed cache file: {file_path}")
                except Exception as e:
                    print(f"Error removing cache file: {e}")
