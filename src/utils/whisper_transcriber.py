"""
Custom speech recognition implementation using OpenAI's Whisper model.

This module provides a real-time transcription service using Whisper,
eliminating the need for external API services like AssemblyAI.
"""

import threading
import queue
import time
import datetime
import io
import numpy as np
import whisper
from typing import Dict, Any, Optional, Callable, List, Tuple

# Import our audio stream adapter
from src.utils.realtime_sink import AudioStreamAdapter


class WhisperTranscriber:
    """Handles real-time transcription for a user using Whisper."""

    def __init__(self,
                 user_id: int,
                 username: str,
                 sample_rate: int = 16000,
                 on_transcript: Optional[Callable[[Dict[str, Any]], None]] = None,
                 model_size: str = "base"):
        """Initialize the Whisper transcriber.

        Args:
            user_id: User ID
            username: Username for display
            sample_rate: Audio sample rate
            on_transcript: Callback function for new transcripts
            model_size: Whisper model size (tiny, base, small, medium, large)
        """
        self.user_id = user_id
        self.username = username
        self.sample_rate = sample_rate
        self.on_transcript = on_transcript
        self.model_size = model_size

        # Transcription components
        self.audio_stream = None
        self.stream_thread = None
        self.processing_thread = None
        
        # Audio buffer for processing
        self.audio_buffer = bytearray()
        self.audio_buffer_lock = threading.Lock()
        
        # Processing queue for audio chunks
        self.processing_queue = queue.Queue()
        
        # State tracking
        self.active = False
        self.last_activity_time = 0
        self.silence_threshold = 2.0  # seconds of silence before considering inactive
        
        # Pending audio for when we're restarting
        self.pending_audio = []
        self.max_pending_audio = 50  # Maximum number of audio chunks to buffer
        
        # Restart tracking
        self.restart_count = 0
        self.last_restart_time = 0
        self.min_restart_interval = 5.0  # Minimum seconds between restarts
        
        # Load the Whisper model
        print(f"Loading Whisper model ({model_size})...")
        self.model = whisper.load_model(model_size)
        print(f"Whisper model loaded: {model_size}")

    def start(self):
        """Start the transcription process."""
        if self.active:
            print(f"Transcriber for user {self.user_id} is already active")
            return

        print(f"Starting transcription for user {self.user_id}")
        
        self.active = True
        
        # Create an audio stream adapter
        self.audio_stream = AudioStreamAdapter(
            sample_rate=self.sample_rate,
            chunk_size=600  # Use smaller chunks by default
        )
        
        # Start the processing thread
        self.processing_thread = threading.Thread(
            target=self._process_audio_chunks,
            daemon=True
        )
        self.processing_thread.start()
        
        # Reset activity time
        self.last_activity_time = time.time()
        
        # Notify that we've started
        if self.on_transcript:
            self.on_transcript({
                'user_id': self.user_id,
                'username': self.username,
                'text': "[Transcription started]",
                'timestamp': datetime.datetime.now().isoformat(),
                'is_final': True
            })

    def stop(self):
        """Stop the transcription process."""
        if not self.active:
            return

        print(f"Stopping transcription for user {self.user_id}")
        
        # Mark as inactive
        self.active = False
        
        # Close the audio stream
        if self.audio_stream:
            self.audio_stream.close()
            
        # Wait for the processing thread to finish (with timeout)
        if self.processing_thread and self.processing_thread.is_alive():
            try:
                self.processing_thread.join(timeout=1.0)
            except Exception as e:
                print(f"Error joining processing thread for user {self.user_id}: {e}")
        
        # Clear any pending audio
        self.pending_audio.clear()
        
        # Notify that we've stopped
        if self.on_transcript:
            self.on_transcript({
                'user_id': self.user_id,
                'username': self.username,
                'text': "[Transcription stopped]",
                'timestamp': datetime.datetime.now().isoformat(),
                'is_final': True
            })

    def add_audio(self, audio_data: bytes):
        """Add audio data to the stream.

        Args:
            audio_data: Audio bytes to process
        """
        # Always update activity time when receiving audio
        self.last_activity_time = time.time()

        # If not active or no stream, buffer the audio for later
        if not self.active or not self.audio_stream:
            # Store audio in pending buffer if we're in the process of restarting
            if len(self.pending_audio) < self.max_pending_audio:
                self.pending_audio.append(audio_data)
            return

        # Add the audio data to the stream
        self.audio_stream.add_audio(audio_data)
        
        # Also add to the processing queue
        with self.audio_buffer_lock:
            self.audio_buffer.extend(audio_data)
            
            # If we have enough audio data, add it to the processing queue
            if len(self.audio_buffer) >= self.sample_rate * 2:  # 1 second of audio (16-bit)
                # Convert to numpy array for Whisper
                audio_np = np.frombuffer(self.audio_buffer, np.int16).astype(np.float32) / 32768.0
                
                # Add to processing queue
                self.processing_queue.put(audio_np)
                
                # Clear the buffer
                self.audio_buffer = bytearray()

    def is_active(self) -> bool:
        """Check if this user is actively speaking.

        Returns:
            bool: True if the user is active, False otherwise
        """
        if not self.active:
            return False

        # Check if we've received audio recently
        current_time = time.time()
        return (current_time - self.last_activity_time) < self.silence_threshold

    def _process_audio_chunks(self):
        """Process audio chunks from the queue."""
        while self.active:
            try:
                # Get audio chunk from the queue with timeout
                try:
                    audio_np = self.processing_queue.get(timeout=0.5)
                except queue.Empty:
                    continue
                
                # Transcribe the audio
                result = self.model.transcribe(audio_np, language="en")
                
                # If we have text, send it to the callback
                if result["text"].strip():
                    timestamp = datetime.datetime.now().isoformat()
                    
                    if self.on_transcript:
                        self.on_transcript({
                            'user_id': self.user_id,
                            'username': self.username,
                            'text': result["text"].strip(),
                            'timestamp': timestamp,
                            'is_final': True
                        })
            except Exception as e:
                print(f"Error processing audio for user {self.user_id}: {e}")
                
                # Notify of the error
                if self.on_transcript:
                    self.on_transcript({
                        'user_id': self.user_id,
                        'username': self.username,
                        'error': f"Audio processing error: {e}",
                        'timestamp': datetime.datetime.now().isoformat()
                    })
                
                # Sleep briefly to avoid tight loop on errors
                time.sleep(0.1)


class WhisperBatchTranscriber:
    """Handles batch transcription of audio files using Whisper."""
    
    def __init__(self, model_size: str = "base"):
        """Initialize the batch transcriber.
        
        Args:
            model_size: Whisper model size (tiny, base, small, medium, large)
        """
        self.model_size = model_size
        
        # Load the Whisper model
        print(f"Loading Whisper batch transcription model ({model_size})...")
        self.model = whisper.load_model(model_size)
        print(f"Whisper batch transcription model loaded: {model_size}")
    
    async def transcribe(self, audio_streams: Dict[int, io.BytesIO]) -> Tuple[str, List[str]]:
        """Transcribes audio streams using Whisper.

        Args:
            audio_streams: A dictionary mapping user_id (int) to their audio data (BytesIO).

        Returns:
            A tuple containing:
            - The formatted transcription message string.
            - A list of individual transcription results (for logging/debugging).
        """
        transcription_results: List[str] = []
        transcription_message: str = ""

        if not audio_streams:
            transcription_message = "\n\nNo audio data captured for transcription."
            return transcription_message, transcription_results
        
        print(f"Starting transcription for {len(audio_streams)} audio file(s)...")
        
        for user_id, audio_stream in audio_streams.items():
            try:
                # Ensure stream is at the beginning
                audio_stream.seek(0)
                
                # Read the audio data
                audio_data = audio_stream.read()
                
                # Convert to numpy array for Whisper
                audio_np = np.frombuffer(audio_data, np.int16).astype(np.float32) / 32768.0
                
                # Transcribe the audio
                result = self.model.transcribe(audio_np, language="en")
                
                # Process transcription results
                if result["text"].strip():
                    result_text = f"<@{user_id}>: {result['text'].strip()}"
                    transcription_results.append(result_text)
                    print(f"Transcription success for {user_id}.")
                else:
                    result_text = f"<@{user_id}>: (No speech detected)"
                    transcription_results.append(result_text)
                    print(f"Transcription complete for {user_id}, but no speech detected.")
                
            except Exception as e:
                print(f"Error during transcription for user {user_id}: {e}")
                transcription_results.append(f"<@{user_id}>: Transcription error.")
            finally:
                # Close the stream
                audio_stream.close()
        
        # Construct the final transcription message
        if transcription_results:
            transcription_details = "\n".join(transcription_results)
            transcription_message = f"\n\n**Transcriptions:**\n{transcription_details}"
        else:
            transcription_message = "\n\nNo speech detected in the recordings."
        
        return transcription_message, transcription_results
