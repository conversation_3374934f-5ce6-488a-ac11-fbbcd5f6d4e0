"""
Text-to-speech integration for Sidenvoice web application using custom TTS.

This module handles:
1. Converting text to speech using local Coqui TTS
2. Caching speech audio to reduce processing time
3. Providing audio files for web playback
"""

import os
import time
import hashlib
import asyncio
import logging
from typing import Optional, Dict, Any
from pathlib import Path

from src.config.ai_config import (
    CUSTOM_TTS_ENABLED,
    CUSTOM_TTS_MODEL,
    CUSTOM_TTS_VOCODER,
    CUSTOM_TTS_SAMPLE_RATE,
    TTS_CACHE_ENABLED,
    TTS_CACHE_DIR
)
from src.utils.text_processing import sanitize_markdown
from src.utils.custom_tts import tts_manager

# Set up logging
logger = logging.getLogger('sidenvoice.web_tts')
if not logger.handlers:
    handler = logging.StreamHandler()
    formatter = logging.Formatter('%(asctime)s - %(name)s - %(levelname)s - %(message)s')
    handler.setFormatter(formatter)
    logger.addHandler(handler)
    logger.setLevel(logging.INFO)

class WebCustomTTS:
    """Handles text-to-speech using custom TTS for web application."""

    def __init__(self):
        """Initialize the TTS engine."""
        self.enabled = CUSTOM_TTS_ENABLED
        self.model = CUSTOM_TTS_MODEL
        self.vocoder = CUSTOM_TTS_VOCODER
        self.sample_rate = CUSTOM_TTS_SAMPLE_RATE

        # Cache settings
        self.cache_enabled = TTS_CACHE_ENABLED
        self.cache_dir = "static/audio"  # Store in static folder for web access
        self.web_audio_path = "static/audio"

        # Initialize cache directory if enabled
        if self.cache_enabled:
            os.makedirs(self.cache_dir, exist_ok=True)
            logger.info(f"TTS cache directory initialized at {self.cache_dir}")

        # Initialize the TTS engine
        try:
            # Load the TTS model
            self.tts_engine = tts_manager.get_engine(self.model)
            logger.info(f"Custom TTS initialized with model: {self.model}")
        except Exception as e:
            logger.error(f"Error initializing Custom TTS: {e}")
            self.enabled = False

        if self.enabled:
            logger.info("Custom TTS initialized with full functionality")
        else:
            logger.info("Custom TTS is disabled")

    async def generate_speech(self,
                             text: str,
                             voice_id: str = None) -> Optional[str]:
        """Generate speech from text.

        Args:
            text: Text to convert to speech
            voice_id: Voice ID parameter (ignored in custom TTS, kept for compatibility)

        Returns:
            str: Path to the audio file or None if generation failed
        """
        if not self.enabled:
            logger.warning("TTS is disabled, cannot generate speech")
            return None

        if not text:
            logger.warning("Cannot generate speech from empty text")
            return None

        # Sanitize the text to remove any markdown formatting
        text = sanitize_markdown(text)

        # Truncate text if it's too long
        if len(text) > 800:
            text = self._truncate_text(text, 800)

        # Generate a cache key for this text
        cache_key = self._generate_cache_key(text)
        cache_path = os.path.join(self.cache_dir, f"{cache_key}.wav")

        # Check cache first if enabled
        if self.cache_enabled and os.path.exists(cache_path):
            logger.info(f"Using cached TTS audio for: {text[:30]}...")
            return cache_path

        try:
            logger.info(f"Generating new TTS audio for: {text[:30]}...")

            # Use the TTS engine to generate speech
            audio_path = await self.tts_engine.generate_speech(text, cache_path)

            # Verify the file was created and has content
            if os.path.exists(audio_path) and os.path.getsize(audio_path) > 0:
                logger.info(f"Verified audio file exists and has content: {audio_path}")
                return audio_path
            else:
                logger.error(f"Error: Audio file was not created or is empty: {audio_path}")
                return None

        except Exception as e:
            logger.error(f"Error generating speech: {e}")
            import traceback
            logger.error(f"Traceback: {traceback.format_exc()}")
            return None

    def _truncate_text(self, text: str, max_chars: int = 800) -> str:
        """Truncate text to a maximum length for TTS, ending at a natural sentence break.

        Args:
            text: Text to truncate
            max_chars: Maximum number of characters

        Returns:
            str: Truncated text with appended message if truncated
        """
        if len(text) <= max_chars:
            return text

        # Try to find a sentence end
        breakpoint = text.rfind('.', 0, max_chars - 50)
        if breakpoint == -1:  # No period found
            breakpoint = text.rfind('!', 0, max_chars - 50)
        if breakpoint == -1:  # No exclamation found
            breakpoint = text.rfind('?', 0, max_chars - 50)
        if breakpoint == -1:  # No question mark found
            breakpoint = text.rfind(' ', 0, max_chars - 50)

        if breakpoint != -1:
            truncated_text = text[:breakpoint+1]
        else:
            truncated_text = text[:max_chars-50]

        # Add standard message about truncation
        truncation_message = " The response is too long to read completely."
        truncated_text += truncation_message

        logger.info(f"Truncated text from {len(text)} to {len(truncated_text)} characters for TTS")
        return truncated_text

    def _generate_cache_key(self, text: str) -> str:
        """Generate a cache key for the text.

        Args:
            text: Text to convert to speech

        Returns:
            str: Cache key
        """
        # Create a hash of the text and model
        hash_input = f"{text}_{self.model}"
        return hashlib.md5(hash_input.encode()).hexdigest()

    def get_web_path(self, file_path: str) -> str:
        """Convert a file path to a web path.

        Args:
            file_path: Path to the audio file

        Returns:
            Web path to the audio file
        """
        # Extract the filename from the path
        filename = os.path.basename(file_path)

        # Return the web path
        return f"/audio/{filename}"


# Create a singleton instance
tts = WebCustomTTS()


async def generate_speech(text: str) -> Optional[Dict[str, Any]]:
    """Generate speech from text.

    Args:
        text: Text to convert to speech

    Returns:
        Dictionary with audio information, or None if generation failed
    """
    # Log the text we're trying to convert
    logger.info(f"Generating speech for text: '{text}'")

    # Always try to use the real TTS engine first
    try:
        # Generate speech using the TTS engine
        audio_path = await tts.generate_speech(text)

        # If generation succeeded, return the result
        if audio_path and os.path.exists(audio_path) and os.path.getsize(audio_path) > 0:
            # Get the web path
            web_path = tts.get_web_path(audio_path)
            logger.info(f"Successfully generated real speech at {audio_path} with web path {web_path}")

            # Return the audio information
            return {
                'path': audio_path,
                'url': web_path,
                'text': text
            }
        else:
            logger.warning(f"TTS generation failed or produced empty file for text: '{text}'")

    except Exception as e:
        logger.exception(f"Error in TTS generation: {e}")

    # If TTS failed, create a simple fallback that indicates the issue
    logger.warning(f"TTS failed for '{text}', creating fallback beep")

    # Create a fallback audio file with beeps
    import wave
    import math
    import hashlib

    # Create a unique filename for this text
    text_hash = hashlib.md5(text.encode()).hexdigest()[:8]
    fallback_path = os.path.join("static/audio", f"fallback_{text_hash}.wav")

    # Check if we already have this fallback file
    if not os.path.exists(fallback_path):
        logger.info(f"Creating fallback beep audio for '{text}' at {fallback_path}")

        # Create a simple beep pattern (different from real speech)
        # This will help you identify when TTS is failing
        duration = 0.5  # seconds
        sample_rate = 22050
        frequency = 800  # Hz (higher pitch to distinguish from real speech)
        amplitude = 16383  # Half amplitude to be less jarring

        # Create the audio data
        num_samples = int(duration * sample_rate)
        data = bytearray()

        for i in range(num_samples):
            t = float(i) / sample_rate
            # Create a beep that fades in and out
            fade_factor = min(t * 10, (duration - t) * 10, 1.0)
            value = int(amplitude * fade_factor * math.sin(2 * math.pi * frequency * t))
            # Convert to 16-bit PCM
            data.extend(value.to_bytes(2, byteorder='little', signed=True))

        # Write the WAV file
        with wave.open(fallback_path, 'wb') as wf:
            wf.setnchannels(1)  # Mono
            wf.setsampwidth(2)  # 16-bit
            wf.setframerate(sample_rate)
            wf.writeframes(data)

    # Return the fallback audio
    web_path = f"/audio/fallback_{text_hash}.wav"
    logger.info(f"Using fallback beep audio at {fallback_path} with web path {web_path}")

    return {
        'path': fallback_path,
        'url': web_path,
        'text': text
    }
