"""
Text-to-speech integration for Sidenvoice web application using ElevenLabs.

This module handles:
1. Converting text to speech using ElevenLabs API
2. Caching speech audio to reduce API calls
3. Providing audio files for web playback
"""

import os
import time
import hashlib
import asyncio
import aiohttp
from typing import Optional

from src.config.ai_config import (
    ELEVENLABS_API_KEY,
    ELEVENLABS_ENABLED,
    ELEVENLABS_VOICE_ID,
    ELEVENLABS_MODEL_ID,
    ELEVENLABS_STABILITY,
    ELEVENLABS_SIMILARITY_BOOST,
    TTS_CACHE_ENABLED,
    TTS_CACHE_DIR
)
from src.utils.text_processing import sanitize_markdown

class WebElevenLabsTTS:
    """Handles text-to-speech using ElevenLabs API for web application."""

    def __init__(self, api_key: str = ELEVENLABS_API_KEY):
        """Initialize the TTS integration.

        Args:
            api_key: ElevenLabs API key
        """
        self.api_key = api_key
        self.voice_id = ELEVENLABS_VOICE_ID
        self.model_id = ELEVENLABS_MODEL_ID
        self.stability = ELEVENLABS_STABILITY
        self.similarity_boost = ELEVENLABS_SIMILARITY_BOOST

        # API endpoints
        self.base_url = "https://api.elevenlabs.io/v1"
        self.tts_endpoint = f"{self.base_url}/text-to-speech"

        # Cache settings
        self.cache_enabled = TTS_CACHE_ENABLED
        self.cache_dir = "static/audio"  # Store in static folder for web access
        self.web_audio_path = "static/audio"

        # Rate limiting
        self.last_request_time = 0
        self.min_request_interval = 1.0  # seconds

        # Initialize cache directory if enabled
        if self.cache_enabled:
            os.makedirs(self.cache_dir, exist_ok=True)
            print(f"TTS cache directory initialized at {self.cache_dir}")

        # Set enabled status
        self.enabled = ELEVENLABS_ENABLED

        # Check if we have an API key for generating new TTS
        self.can_generate = bool(self.api_key)

        if self.enabled:
            if self.can_generate:
                print("ElevenLabs TTS initialized with full functionality")
            else:
                print("ElevenLabs TTS initialized in playback-only mode (no API key for new generation)")
        else:
            print("ElevenLabs TTS integration is disabled")

    async def generate_speech(self,
                             text: str,
                             voice_id: str = None) -> Optional[str]:
        """Generate speech from text.

        Args:
            text: Text to convert to speech
            voice_id: Voice ID to use (defaults to configured voice)

        Returns:
            str: Path to the audio file or None if generation failed
        """
        if not self.enabled:
            print("TTS is disabled, cannot generate speech")
            return None

        if not text:
            print("Cannot generate speech from empty text")
            return None

        # Use default voice if not specified
        voice_id = voice_id or self.voice_id

        # Sanitize the text to remove any markdown formatting
        text = sanitize_markdown(text)

        # Truncate text if it's too long
        if len(text) > 800:
            text = self._truncate_text(text, 800)

        # Generate a cache key for this text/voice combination
        cache_key = self._generate_cache_key(text, voice_id)
        cache_path = os.path.join(self.cache_dir, f"{cache_key}.mp3")

        # Check cache first if enabled
        if self.cache_enabled and os.path.exists(cache_path):
            print(f"Using cached TTS audio for: {text[:30]}...")
            return cache_path

        # If we don't have an API key, we can't generate new speech
        if not self.can_generate:
            print("No API key available, cannot generate new speech")
            # For testing purposes, use a sample audio file
            sample_path = os.path.join(self.cache_dir, "sample_response.mp3")
            if os.path.exists(sample_path):
                print(f"Using sample audio file: {sample_path}")
                return sample_path
            return None

        # Prepare request
        url = f"{self.tts_endpoint}/{voice_id}"

        headers = {
            "Accept": "audio/mpeg",
            "Content-Type": "application/json",
            "xi-api-key": self.api_key
        }

        payload = {
            "text": text,
            "model_id": self.model_id,
            "voice_settings": {
                "stability": self.stability,
                "similarity_boost": self.similarity_boost,
                "speed": 1.2
            }
        }

        try:
            print(f"Generating new TTS audio for: {text[:30]}...")
            async with aiohttp.ClientSession() as session:
                async with session.post(url, json=payload, headers=headers) as response:
                    if response.status == 200:
                        audio_data = await response.read()

                        # Save the audio data to a file
                        with open(cache_path, 'wb') as f:
                            f.write(audio_data)
                        print(f"Saved TTS audio to: {cache_path}")

                        # Verify the file was created and has content
                        if os.path.exists(cache_path) and os.path.getsize(cache_path) > 0:
                            print(f"Verified audio file exists and has content: {cache_path}")
                            return cache_path
                        else:
                            print(f"Error: Audio file was not created or is empty: {cache_path}")
                            return None
                    else:
                        error_text = await response.text()
                        print(f"ElevenLabs API error ({response.status}): {error_text}")
                        return None

        except Exception as e:
            print(f"Error generating speech: {e}")
            import traceback
            print(f"Traceback: {traceback.format_exc()}")
            return None

    def _truncate_text(self, text: str, max_chars: int = 800) -> str:
        """Truncate text to a maximum length for TTS, ending at a natural sentence break.

        Args:
            text: Text to truncate
            max_chars: Maximum number of characters

        Returns:
            str: Truncated text with appended message if truncated
        """
        if len(text) <= max_chars:
            return text

        # Try to find a sentence end
        breakpoint = text.rfind('.', 0, max_chars - 50)
        if breakpoint == -1:  # No period found
            breakpoint = text.rfind('!', 0, max_chars - 50)
        if breakpoint == -1:  # No exclamation found
            breakpoint = text.rfind('?', 0, max_chars - 50)
        if breakpoint == -1:  # No question mark found
            breakpoint = text.rfind(' ', 0, max_chars - 50)

        if breakpoint != -1:
            truncated_text = text[:breakpoint+1]
        else:
            truncated_text = text[:max_chars-50]

        # Add standard message about truncation
        truncation_message = " The response is too long to read completely."
        truncated_text += truncation_message

        print(f"Truncated text from {len(text)} to {len(truncated_text)} characters for TTS")
        return truncated_text

    def _generate_cache_key(self, text: str, voice_id: str) -> str:
        """Generate a cache key for the text and voice.

        Args:
            text: Text to convert to speech
            voice_id: Voice ID to use

        Returns:
            str: Cache key
        """
        # Create a hash of the text and voice ID
        hash_input = f"{text}_{voice_id}_{self.model_id}_{self.stability}_{self.similarity_boost}"
        return hashlib.md5(hash_input.encode()).hexdigest()
