import os
from dotenv import load_dotenv

# Load environment variables from .env file
load_dotenv()

# --- Speech Recognition Configuration ---
# Using Whisper for local speech recognition
WHISPER_ENABLED: bool = True
WHISPER_MODEL_SIZE: str = os.getenv('WHISPER_MODEL_SIZE', 'base')  # Options: tiny, base, small, medium, large

# Configure speech recognition
print(f"Using Whisper for speech recognition with model size: {WHISPER_MODEL_SIZE}")
print("Local speech recognition is enabled - no API key required.")

# --- Application Settings ---
FLASK_SECRET_KEY: str = os.getenv('FLASK_SECRET_KEY', 'sidenvoice-secret-key')
UPLOAD_FOLDER: str = os.path.join(os.path.dirname(os.path.dirname(os.path.dirname(__file__))), 'static', 'audio')
ALLOWED_EXTENSIONS: set = {'wav', 'mp3', 'ogg'}

# Ensure upload directory exists
os.makedirs(UPLOAD_FOLDER, exist_ok=True)