import os
from dotenv import load_dotenv
import assemblyai as aai

# Load environment variables from .env file
load_dotenv()

# --- AssemblyAI Configuration (for speech-to-text) ---
ASSEMBLYAI_API_KEY: str | None = os.getenv('ASSEMBLYAI_API_KEY')
ASSEMBLYAI_ENABLED: bool = False

if not ASSEMBLYAI_API_KEY:
    print("Warning: ASSEMBLYAI_API_KEY environment variable not set or found in .env. Transcription feature will be disabled.")
else:
    # Try to configure the AssemblyAI SDK
    try:
        aai.settings.api_key = ASSEMBLYAI_API_KEY
        ASSEMBLYAI_ENABLED = True
        print("AssemblyAI SDK configured successfully.")
    except Exception as e:
        print(f"Error configuring AssemblyAI SDK: {e}. Transcription feature will be disabled.")
        ASSEMBLYAI_ENABLED = False

# --- Application Settings ---
FLASK_SECRET_KEY: str = os.getenv('FLASK_SECRET_KEY', 'sidenvoice-secret-key')
UPLOAD_FOLDER: str = os.path.join(os.path.dirname(os.path.dirname(os.path.dirname(__file__))), 'static', 'audio')
ALLOWED_EXTENSIONS: set = {'wav', 'mp3', 'ogg'}

# Ensure upload directory exists
os.makedirs(UPLOAD_FOLDER, exist_ok=True)