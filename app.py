"""
Sidenvoice - Web-based AI Voice Chat Application

This application provides a web interface for users to interact with an AI assistant
using voice input and output.
"""

import os
import logging
import sys
import time
import json
import asyncio
from flask import Flask, render_template, request, jsonify, send_from_directory
from flask_socketio import SocketIO

# Import configuration
from src.config.config import (
    FLASK_SECRET_KEY,
    UPLOAD_FOLDER,
    ALLOWED_EXTENSIONS
)

# Import AI integration
from src.utils.web_ai_integration import WebGeminiAI
from src.utils.web_tts import generate_speech

# Setup logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler(sys.stdout)
    ]
)

logger = logging.getLogger('sidenvoice')

def allowed_file(filename):
    return '.' in filename and \
           filename.rsplit('.', 1)[1].lower() in ALLOWED_EXTENSIONS

# Initialize Flask app
app = Flask(__name__,
            static_folder='static',
            template_folder='templates')
app.config['SECRET_KEY'] = FLASK_SECRET_KEY
app.config['UPLOAD_FOLDER'] = UPLOAD_FOLDER
app.config['MAX_CONTENT_LENGTH'] = 16 * 1024 * 1024  # 16MB max file size

# Initialize Socket.IO with CORS enabled
socketio = SocketIO(app, cors_allowed_origins="*")

# Initialize AI
ai = WebGeminiAI()
# TTS is already initialized in src/utils/web_tts.py

# Store conversation history
conversation_history = []

@app.route('/')
def index():
    """Render the main page."""
    return render_template('index.html')

@app.route('/audio/<path:filename>')
def serve_audio(filename):
    """Serve audio files from the upload directory."""
    return send_from_directory(app.config['UPLOAD_FOLDER'], filename)

@app.route('/api/create-test-audio', methods=['POST'])
def create_test_audio():
    """Create a test audio file for debugging."""
    try:
        # Create a simple test audio file
        output_path = os.path.join(app.config['UPLOAD_FOLDER'], 'test-audio.wav')

        # Create a simple sine wave using Python's wave module
        import wave
        import math

        # Parameters
        duration = 1  # seconds
        sample_rate = 22050
        frequency = 440  # Hz (A4 note)
        amplitude = 32767  # Max amplitude for 16-bit audio

        # Create the audio data
        num_samples = int(duration * sample_rate)
        data = bytearray()

        for i in range(num_samples):
            t = float(i) / sample_rate
            value = int(amplitude * math.sin(2 * math.pi * frequency * t))
            # Convert to 16-bit PCM
            data.extend(value.to_bytes(2, byteorder='little', signed=True))

        # Write the WAV file
        with wave.open(output_path, 'wb') as wf:
            wf.setnchannels(1)  # Mono
            wf.setsampwidth(2)  # 16-bit
            wf.setframerate(sample_rate)
            wf.writeframes(data)

        logger.info(f"Created test audio file at: {output_path}")

        # Return the URL to the file
        return jsonify({
            'success': True,
            'path': output_path,
            'url': '/audio/test-audio.wav'
        })
    except Exception as e:
        logger.exception(f"Error creating test audio: {e}")
        return jsonify({
            'success': False,
            'error': str(e)
        })

@app.route('/api/check-audio-dir', methods=['GET'])
def check_audio_dir():
    """Check the audio directory for debugging."""
    try:
        audio_dir = app.config['UPLOAD_FOLDER']
        files = os.listdir(audio_dir)

        file_info = []
        for file in files:
            file_path = os.path.join(audio_dir, file)
            if os.path.isfile(file_path):
                file_info.append({
                    'name': file,
                    'size': os.path.getsize(file_path),
                    'path': file_path,
                    'url': f'/audio/{file}',
                    'exists': os.path.exists(file_path)
                })

        return jsonify({
            'success': True,
            'directory': audio_dir,
            'exists': os.path.exists(audio_dir),
            'is_dir': os.path.isdir(audio_dir),
            'file_count': len(file_info),
            'files': file_info
        })
    except Exception as e:
        logger.exception(f"Error checking audio directory: {e}")
        return jsonify({
            'success': False,
            'error': str(e)
        })

@app.route('/api/chat', methods=['POST'])
def chat():
    """Process a chat message and return the AI response."""
    try:
        logger.info("Received chat request")
        data = request.json
        user_message = data.get('message', '')
        username = data.get('username', 'User')

        logger.info(f"User message: '{user_message}'")

        if not user_message:
            logger.warning("No message provided in request")
            return jsonify({'error': 'No message provided'}), 400

        # Add user message to conversation history
        conversation_history.append({
            'username': username,
            'text': user_message,
            'timestamp': time.time()
        })

        # Limit conversation history to last 20 messages
        if len(conversation_history) > 20:
            conversation_history.pop(0)

        logger.info("Processing request with AI...")
        # Process the request with AI
        loop = asyncio.new_event_loop()
        asyncio.set_event_loop(loop)
        ai_response = loop.run_until_complete(
            ai.process_request(user_message, conversation_history)
        )
        logger.info(f"AI response received: {ai_response[:100]}..." if isinstance(ai_response, str) and len(ai_response) > 100 else f"AI response received: {ai_response}")

        # Extract the response text from the AI response dictionary
        if isinstance(ai_response, dict):
            response_text = ai_response.get('response', 'Sorry, I could not generate a response.')
            web_search_performed = ai_response.get('web_search_performed', False)
            if web_search_performed:
                logger.info(f"Web search was performed for query: {user_message}")
        else:
            response_text = str(ai_response)

        # Add AI response to conversation history
        conversation_history.append({
            'username': 'AI',
            'text': response_text,
            'timestamp': time.time()
        })

        # Generate speech for the response
        try:
            audio_result = loop.run_until_complete(
                generate_speech(response_text)
            )

            # Log audio path for debugging
            if audio_result:
                audio_path = audio_result.get('path')
                audio_url = audio_result.get('url')
                logger.info(f"Generated audio file at: {audio_path}")
                logger.info(f"Audio URL: {audio_url}")

                # Verify the file exists
                if os.path.exists(audio_path):
                    logger.info(f"Audio file exists at: {audio_path}")
                else:
                    logger.warning(f"Audio file does not exist at: {audio_path}")
            else:
                logger.warning("No audio result returned from TTS, creating fallback")

                # Create a fallback audio file
                fallback_path = os.path.join(app.config['UPLOAD_FOLDER'], "fallback_response.wav")

                # Create a simple sine wave as fallback
                import wave
                import math

                # Parameters
                duration = 1  # seconds
                sample_rate = 22050
                frequency = 440  # Hz (A4 note)
                amplitude = 32767  # Max amplitude for 16-bit audio

                # Create the audio data
                num_samples = int(duration * sample_rate)
                data = bytearray()

                for i in range(num_samples):
                    t = float(i) / sample_rate
                    value = int(amplitude * math.sin(2 * math.pi * frequency * t))
                    # Convert to 16-bit PCM
                    data.extend(value.to_bytes(2, byteorder='little', signed=True))

                # Write the WAV file
                with wave.open(fallback_path, 'wb') as wf:
                    wf.setnchannels(1)  # Mono
                    wf.setsampwidth(2)  # 16-bit
                    wf.setframerate(sample_rate)
                    wf.writeframes(data)

                audio_url = "/audio/fallback_response.wav"
                logger.info(f"Created fallback audio at {fallback_path} with URL {audio_url}")
        except Exception as e:
            logger.exception(f"Error generating audio: {e}")

            # Create an emergency fallback
            audio_url = "/audio/emergency_fallback.wav"
            emergency_path = os.path.join(app.config['UPLOAD_FOLDER'], "emergency_fallback.wav")

            # Check if we need to create the emergency fallback file
            if not os.path.exists(emergency_path):
                try:
                    # Create a simple sine wave as emergency fallback
                    import wave
                    import math

                    # Parameters
                    duration = 1  # seconds
                    sample_rate = 22050
                    frequency = 440  # Hz (A4 note)
                    amplitude = 32767  # Max amplitude for 16-bit audio

                    # Create the audio data
                    num_samples = int(duration * sample_rate)
                    data = bytearray()

                    for i in range(num_samples):
                        t = float(i) / sample_rate
                        value = int(amplitude * math.sin(2 * math.pi * frequency * t))
                        # Convert to 16-bit PCM
                        data.extend(value.to_bytes(2, byteorder='little', signed=True))

                    # Write the WAV file
                    with wave.open(emergency_path, 'wb') as wf:
                        wf.setnchannels(1)  # Mono
                        wf.setsampwidth(2)  # 16-bit
                        wf.setframerate(sample_rate)
                        wf.writeframes(data)

                    logger.info(f"Created emergency fallback audio at {emergency_path}")
                except Exception as inner_e:
                    logger.exception(f"Error creating emergency fallback: {inner_e}")

        # Always return a response with an audio URL
        return jsonify({
            'response': response_text,
            'audio_url': audio_url
        })

    except Exception as e:
        logger.exception(f"Error processing chat request: {e}")
        return jsonify({'error': str(e)}), 500

@socketio.on('connect')
def handle_connect():
    """Handle WebSocket connection."""
    logger.info(f"Client connected: {request.sid}")

@socketio.on('disconnect')
def handle_disconnect():
    """Handle WebSocket disconnection."""
    logger.info(f"Client disconnected: {request.sid}")

@socketio.on('audio_data')
def handle_audio_data(data):
    """Handle incoming audio data from the client."""
    try:
        # Get the audio data from the client
        audio_bytes = data.get('audio')
        user_id = data.get('user_id', request.sid)
        username = data.get('username', 'User')

        if not audio_bytes:
            logger.warning("Received audio_data event without audio data")
            socketio.emit('audio_status', {'status': 'error', 'message': 'No audio data received'})
            return

        # Process the audio data with our speech recognition system
        # TODO: Implement real-time transcription with WhisperTranscriber
        # For now, we'll just acknowledge receipt
        logger.debug(f"Received {len(audio_bytes)} bytes of audio data from user {user_id}")
        socketio.emit('audio_status', {'status': 'received'})

    except Exception as e:
        logger.exception(f"Error processing audio data: {e}")
        socketio.emit('error', {'message': str(e)})

if __name__ == '__main__':
    # Create necessary directories
    os.makedirs('static/audio', exist_ok=True)

    # Start the Flask app
    logger.info("Starting Sidenvoice web application...")
    socketio.run(app, host='0.0.0.0', port=5006, debug=True)
