"""
Sidenvoice - Web-based AI Voice Chat Application

This application provides a web interface for users to interact with an AI assistant
using voice input and output.
"""

import os
import logging
import sys
import time
import json
import asyncio
from flask import Flask, render_template, request, jsonify, send_from_directory
from flask_socketio import SocketIO

# Import configuration
from src.config.config import (
    FLASK_SECRET_KEY,
    UPLOAD_FOLDER,
    ALLOWED_EXTENSIONS
)

# Import AI integration
from src.utils.web_ai_integration import WebGeminiAI
from src.utils.web_tts import generate_speech

# Setup logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler(sys.stdout)
    ]
)

logger = logging.getLogger('sidenvoice')

def allowed_file(filename):
    return '.' in filename and \
           filename.rsplit('.', 1)[1].lower() in ALLOWED_EXTENSIONS

# Initialize Flask app
app = Flask(__name__,
            static_folder='static',
            template_folder='templates')
app.config['SECRET_KEY'] = FLASK_SECRET_KEY
app.config['UPLOAD_FOLDER'] = UPLOAD_FOLDER
app.config['MAX_CONTENT_LENGTH'] = 16 * 1024 * 1024  # 16MB max file size

# Initialize Socket.IO with CORS enabled
socketio = SocketIO(app, cors_allowed_origins="*")

# Initialize AI
ai = WebGeminiAI()
# TTS is already initialized in src/utils/web_tts.py

# Store conversation history
conversation_history = []

@app.route('/')
def index():
    """Render the main page."""
    return render_template('index.html')

@app.route('/audio/<path:filename>')
def serve_audio(filename):
    """Serve audio files from the upload directory."""
    return send_from_directory(app.config['UPLOAD_FOLDER'], filename)

@app.route('/api/chat', methods=['POST'])
def chat():
    """Process a chat message and return the AI response."""
    try:
        logger.info("Received chat request")
        data = request.json
        user_message = data.get('message', '')
        username = data.get('username', 'User')

        logger.info(f"User message: '{user_message}'")

        if not user_message:
            logger.warning("No message provided in request")
            return jsonify({'error': 'No message provided'}), 400

        # Add user message to conversation history
        conversation_history.append({
            'username': username,
            'text': user_message,
            'timestamp': time.time()
        })

        # Limit conversation history to last 20 messages
        if len(conversation_history) > 20:
            conversation_history.pop(0)

        logger.info("Processing request with AI...")
        # Process the request with AI
        loop = asyncio.new_event_loop()
        asyncio.set_event_loop(loop)
        ai_response = loop.run_until_complete(
            ai.process_request(user_message, conversation_history)
        )
        logger.info(f"AI response received: {ai_response[:100]}..." if isinstance(ai_response, str) and len(ai_response) > 100 else f"AI response received: {ai_response}")

        # Extract the response text from the AI response dictionary
        if isinstance(ai_response, dict):
            response_text = ai_response.get('response', 'Sorry, I could not generate a response.')
            web_search_performed = ai_response.get('web_search_performed', False)
            if web_search_performed:
                logger.info(f"Web search was performed for query: {user_message}")
        else:
            response_text = str(ai_response)

        # Add AI response to conversation history
        conversation_history.append({
            'username': 'AI',
            'text': response_text,
            'timestamp': time.time()
        })

        # Generate speech for the response
        audio_result = loop.run_until_complete(
            generate_speech(response_text)
        )

        # Log audio path for debugging
        if audio_result:
            audio_path = audio_result.get('path')
            audio_url = audio_result.get('url')
            logger.info(f"Generated audio file at: {audio_path}")
            logger.info(f"Audio URL: {audio_url}")

            # Verify the file exists
            if os.path.exists(audio_path):
                logger.info(f"Audio file exists at: {audio_path}")
            else:
                logger.warning(f"Audio file does not exist at: {audio_path}")
        else:
            logger.warning("No audio result returned from TTS")
            audio_url = None

        return jsonify({
            'response': response_text,
            'audio_url': audio_url
        })

    except Exception as e:
        logger.exception(f"Error processing chat request: {e}")
        return jsonify({'error': str(e)}), 500

@socketio.on('connect')
def handle_connect():
    """Handle WebSocket connection."""
    logger.info(f"Client connected: {request.sid}")

@socketio.on('disconnect')
def handle_disconnect():
    """Handle WebSocket disconnection."""
    logger.info(f"Client disconnected: {request.sid}")

@socketio.on('audio_data')
def handle_audio_data(data):
    """Handle incoming audio data from the client."""
    try:
        # Get the audio data from the client
        audio_bytes = data.get('audio')
        user_id = data.get('user_id', request.sid)
        username = data.get('username', 'User')

        if not audio_bytes:
            logger.warning("Received audio_data event without audio data")
            socketio.emit('audio_status', {'status': 'error', 'message': 'No audio data received'})
            return

        # Process the audio data with our speech recognition system
        # TODO: Implement real-time transcription with WhisperTranscriber
        # For now, we'll just acknowledge receipt
        logger.debug(f"Received {len(audio_bytes)} bytes of audio data from user {user_id}")
        socketio.emit('audio_status', {'status': 'received'})

    except Exception as e:
        logger.exception(f"Error processing audio data: {e}")
        socketio.emit('error', {'message': str(e)})

if __name__ == '__main__':
    # Create necessary directories
    os.makedirs('static/audio', exist_ok=True)

    # Start the Flask app
    logger.info("Starting Sidenvoice web application...")
    socketio.run(app, host='0.0.0.0', port=5006, debug=True)
