import logging
import sys
from app import app, socketio

# Setup better logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler(sys.stdout)
    ]
)

logger = logging.getLogger('sidenvoice')

def main():
    """Main function to run the web application."""
    logger.info("Starting Sidenvoice web application...")
    socketio.run(app, debug=True, host='0.0.0.0', port=5000)

if __name__ == "__main__":
    main()